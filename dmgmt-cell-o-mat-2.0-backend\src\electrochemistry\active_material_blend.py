from typing import Optional
from electrochemistry.active_material import ActiveMaterialWeight, MaterialVersion
from electrochemistry.blending import FormationWeight, calculate_blend_formations
from electrochemistry.formation_table import FormationTable
from electrochemistry.formation_table import Formation  # Update this if Formation is defined elsewhere
from electrochemistry.material import MaterialWeight
from electrochemistry.prelithiation import Prelithiation
from models.chart_model import ChartModel

class ActiveMaterialBlend:

    active_materials: list[ActiveMaterialWeight]
    materials: list[MaterialWeight]
    formations: FormationTable

    def __init__(self, active_materials: list[ActiveMaterialWeight], formations: FormationTable, q_aim: Optional[float] = None, q_aim_first_charge: Optional[float] = None, prelithiation: Optional[Prelithiation] = None):

        self.q_aim = q_aim
        self.q_aim_first_charge = q_aim_first_charge
        # normalize weights
        weight_sum = sum(m.weight for m in active_materials)
        active_materials = [ActiveMaterialWeight(m.material, m.weight / weight_sum) for m in active_materials]

        self.formations = formations
        if self.formations is None:
            self.formations = calculate_blend_formations([FormationWeight(m.material.formations, m.weight,m.material) for m in active_materials])
            
        if q_aim is not None:
            factor = q_aim / self.formations.get_formation_c_10_discharge().capacity
            self.formations = self.formations.scale_q(factor)

        if q_aim_first_charge is not None:
            factor = q_aim_first_charge / self.formations.get_formation_first_charge().capacity
            offset = self.formations.get_formation_first_charge().capacity * factor - self.formations.get_formation_first_charge().capacity
            self.formations = self.formations.with_formation_first_charge(self.formations.get_formation_first_charge().scale_q(factor))
            self.formations = FormationTable([(f.translate_q(offset) if f.description != 'Formierung 1. Laden' else f) for f in self.formations.formations])
            # translate q

        self.prelithiation = prelithiation
        if self.prelithiation is not None:
            self.formations = self.formations.apply_prelithiation(self.prelithiation)

        self.active_materials = active_materials

        # unwind materials
        self.materials = [MaterialWeight(material = component.material, weight = material.weight * component.weight) for material in self.active_materials for component in material.material.composition]

    def get_active_surface(self) -> float:
        return sum(m.weight * m.material.active_surface for m in self.active_materials if m.material.active_surface is not None)

    def get_chart_data(self, n_samples: int) -> ChartModel:
        if len(self.materials) == 0:
            return None
        else:
            # Apply voltage constraints to individual material formations
            formation_tables = {}
            for index, m in enumerate(self.active_materials):
                material_formations = m.material.formations
                
                # Apply voltage constraints if they exist
                # if hasattr(m.material, 'v_min') and hasattr(m.material, 'v_max') and m.material.v_min is not None and m.material.v_max is not None:
                    # filtered_formations = []
                    # formation1=material_formations.get_formation('Formierung 1. Laden')
                    # monotonized = formation1.monotonize_u()
                    # q_max_1=formation1.get_q(m.material.v_max)
                    # monotized_q_max_1=monotonized.get_q(m.material.v_max)
                    # print('@@@q_max_1', q_max_1)
                    # print('@@@monotized_q_max_1', monotized_q_max_1)
                    # for formation in material_formations.formations:
                    #     if formation.cycle is not None:
                    #         # Filter data points within voltage range
                    #         voltage_mask = (formation.cycle['SpannungV'] >= m.material.v_min) & (formation.cycle['SpannungV'] <= m.material.v_max) & (formation.cycle['LadungMAhg'] <= q_max_1)
                    #         if voltage_mask.any():
                    #             filtered_cycle = formation.cycle.loc[voltage_mask].reset_index(drop=True)
                    #             filtered_formations.append(Formation(formation.description, filtered_cycle))
                    #         else:
                    #             # No data within voltage range
                    #             filtered_formations.append(Formation(formation.description, None))
                    #     else:
                    #         filtered_formations.append(formation)
                    
                    # material_formations = FormationTable(filtered_formations)
                
                formation_tables[f'material{index + 1}'] = (m.material.name, material_formations)
            
            if len(self.active_materials) > 1:
                formation_tables = { 'blend': ('Blend', self.formations) } | formation_tables
            elif self.q_aim is not None or self.q_aim_first_charge is not None:
                formation_tables = { 'Modified value by target': ('Modified value by target', self.formations) } | formation_tables
            return FormationTable.get_prefixed_chart_data(formation_tables, n_samples)

    def get_summary_table(self, voltage_range: tuple[float, float] | tuple[None, None],material_version: MaterialVersion) -> dict:
        overlaping_voltage_range = self.get_overlapping_voltage_range()

        if overlaping_voltage_range is not None:
            voltage_range = overlaping_voltage_range
        print('@@@voltage_range final', voltage_range)

        if len(self.active_materials) == 0:
            return None
        else:
            formation_tables = { m.material.name: m.material.formations for m in self.active_materials }
            if len(self.active_materials) > 1:
                formation_tables = { 'Blend': self.formations } | formation_tables
            elif self.q_aim is not None or self.q_aim_first_charge is not None:
                formation_tables = { 'Modified value by target': self.formations } | formation_tables
            return self.formations.get_prefixed_summary_table(
                formation_tables = formation_tables,
                voltage_range = voltage_range
            ) if material_version != MaterialVersion.V1_0.value else self.formations.get_prefixed_summary_table_new_material(
                formation_tables = formation_tables,
                voltage_range = voltage_range
            )
        
    def get_material_voltage_ranges(self):
        """Get voltage ranges for all materials"""
        ranges = []
        for material in self.active_materials:
            if hasattr(material.material, 'v_min') and hasattr(material.material, 'v_max') and material.material.v_min is not None and material.material.v_max is not None:
                ranges.append([material.material.v_min, material.material.v_max])
            else:
                ranges.append(None)
        return ranges
    
    def get_overlapping_voltage_range(self):
        """Get overlapping voltage range for multiple materials"""
        valid_ranges = []
        for material in self.active_materials:
            v_min = getattr(material.material, 'v_min', None)
            v_max = getattr(material.material, 'v_max', None)
            if v_min is not None and v_max is not None:
                valid_ranges.append([v_min, v_max])

        if not valid_ranges:
            return None  # No valid ranges found

        if len(valid_ranges) == 1:
            return valid_ranges[0]  # Only one valid range, return it directly

        # Compute overlapping range across multiple materials
        min_voltage = max(r[0] for r in valid_ranges)  # highest minimum
        max_voltage = min(r[1] for r in valid_ranges)  # lowest maximum

        if min_voltage <= max_voltage:
            return [min_voltage, max_voltage]
        else:
            return None  # No overlap


    @property
    def composite_density(self) -> float:
        return sum(m.weight for m in self.active_materials) / sum(m.weight / m.material.density for m in self.active_materials)
