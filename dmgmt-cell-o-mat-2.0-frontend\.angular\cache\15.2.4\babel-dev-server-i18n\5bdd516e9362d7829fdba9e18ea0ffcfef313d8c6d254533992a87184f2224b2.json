{"ast": null, "code": "import { Control<PERSON>ontainer, NgModelGroup } from '@angular/forms';\nimport { LINE_CHART_OPTIONS } from '@com/const';\nimport { deepEquals } from '@com/utils/deep-equals';\nimport { deepCloneDto } from '@com/utils/object';\nimport { validateQValuesMinimumError, validateQValuesComparisonError } from '@com/app/shared/directive/validators/validate-q-values.directive';\nimport { validateGroupSumErrorActualNumber } from '@com/app/shared/directive/validators/validate-group-sum.directive';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../shared/directive/validators/validate-group-sum.directive\";\nimport * as i2 from \"../../shared/directive/validators/validate-q-values.directive\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/forms\";\nimport * as i5 from \"../../prime-ng/customize-prime-input.directive\";\nimport * as i6 from \"primeng/api\";\nimport * as i7 from \"primeng/dropdown\";\nimport * as i8 from \"primeng/inputnumber\";\nimport * as i9 from \"primeng/card\";\nimport * as i10 from \"primeng/inputswitch\";\nimport * as i11 from \"primeng/progressspinner\";\nimport * as i12 from \"../summary-table/summary-table.component\";\nimport * as i13 from \"../material-chart/material-chart.component\";\nfunction CathodeEditorComponent_div_0_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵi18n(1, 34);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵi18nExp(ctx_r2.metrics == null ? null : ctx_r2.metrics.cathode == null ? null : ctx_r2.metrics.cathode.materialVoltageRanges == null ? null : ctx_r2.metrics.cathode.materialVoltageRanges[0] == null ? null : ctx_r2.metrics.cathode.materialVoltageRanges[0][0])(ctx_r2.metrics == null ? null : ctx_r2.metrics.cathode == null ? null : ctx_r2.metrics.cathode.materialVoltageRanges == null ? null : ctx_r2.metrics.cathode.materialVoltageRanges[0] == null ? null : ctx_r2.metrics.cathode.materialVoltageRanges[0][1]);\n    i0.ɵɵi18nApply(1);\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const selectedVersion_r11 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", selectedVersion_r11.description, \" - \", i0.ɵɵpipeBind1(2, 2, selectedVersion_r11.date), \"\");\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_template_22_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const version_r12 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", version_r12.description, \" - \", i0.ɵɵpipeBind1(2, 2, version_r12.date), \"\");\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 48);\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_8_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵi18n(1, 52);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_8_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵi18n(1, 53);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"span\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CathodeEditorComponent_div_0_ng_container_23_div_8_span_3_Template, 2, 0, \"span\", 19);\n    i0.ɵɵtemplate(4, CathodeEditorComponent_div_0_ng_container_23_div_8_span_4_Template, 2, 0, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r13 = i0.ɵɵreference(6);\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(4);\n    const ctx_r15 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (_r13.errors == null ? null : _r13.errors[ctx_r15.fieldRequiredError]) && _r13.touched);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(_r13.errors == null ? null : _r13.errors[ctx_r15.fieldRequiredError]) && (_r1.errors == null ? null : _r1.errors[ctx_r15.groupSumErrorName]));\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 33);\n    i0.ɵɵi18n(1, 54);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r16 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵi18nExp(ctx_r16.metrics == null ? null : ctx_r16.metrics.cathode == null ? null : ctx_r16.metrics.cathode.materialVoltageRanges == null ? null : ctx_r16.metrics.cathode.materialVoltageRanges[1] == null ? null : ctx_r16.metrics.cathode.materialVoltageRanges[1][0])(ctx_r16.metrics == null ? null : ctx_r16.metrics.cathode == null ? null : ctx_r16.metrics.cathode.materialVoltageRanges == null ? null : ctx_r16.metrics.cathode.materialVoltageRanges[1] == null ? null : ctx_r16.metrics.cathode.materialVoltageRanges[1][1]);\n    i0.ɵɵi18nApply(1);\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_ng_template_20_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const selectedVersion_r25 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", selectedVersion_r25.description, \" - \", i0.ɵɵpipeBind1(2, 2, selectedVersion_r25.date), \"\");\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_ng_template_21_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"date\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const version_r26 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate2(\"\", version_r26.description, \" - \", i0.ɵɵpipeBind1(2, 2, version_r26.date), \"\");\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 48);\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_29_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵi18n(1, 55);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_29_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵi18n(1, 56);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"span\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CathodeEditorComponent_div_0_ng_container_23_div_29_span_3_Template, 2, 0, \"span\", 19);\n    i0.ɵɵtemplate(4, CathodeEditorComponent_div_0_ng_container_23_div_29_span_4_Template, 2, 0, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r19 = i0.ɵɵreference(27);\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(4);\n    const ctx_r21 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", (_r19.errors == null ? null : _r19.errors[ctx_r21.fieldRequiredError]) && _r19.touched);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(_r19.errors == null ? null : _r19.errors[ctx_r21.fieldRequiredError]) && (_r1.errors == null ? null : _r1.errors[ctx_r21.groupSumErrorName]));\n  }\n}\nfunction CathodeEditorComponent_div_0_ng_container_23_div_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"label\", 35);\n    i0.ɵɵi18n(2, 57);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 8);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r22 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"for\", ctx_r22.controlNames.cathodeMaterial2Weight);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r22.metrics.cathode.overlappingVoltageRange[0], \"V - \", ctx_r22.metrics.cathode.overlappingVoltageRange[1], \"V \");\n  }\n}\nconst _c32 = function (a0) {\n  return {\n    \"ng-invalid ng-dirty\": a0\n  };\n};\nfunction CathodeEditorComponent_div_0_ng_container_23_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r30 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"div\", 5)(2, \"label\", 35);\n    i0.ɵɵi18n(3, 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 8)(5, \"p-inputNumber\", 37, 38);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_ng_container_23_Template_p_inputNumber_ngModelChange_5_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r29 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r29.design.cathodeMaterials[0].weightPercent = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, CathodeEditorComponent_div_0_ng_container_23_div_7_Template, 1, 0, \"div\", 28);\n    i0.ɵɵtemplate(8, CathodeEditorComponent_div_0_ng_container_23_div_8_Template, 5, 2, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 5)(10, \"label\", 39);\n    i0.ɵɵi18n(11, 40);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 8)(13, \"p-dropdown\", 41);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_ng_container_23_Template_p_dropdown_ngModelChange_13_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r31 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r31.design.cathodeMaterials[1].materialId = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(14, CathodeEditorComponent_div_0_ng_container_23_div_14_Template, 2, 2, \"div\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(15, \"div\", 5)(16, \"label\", 42);\n    i0.ɵɵi18n(17, 43);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 8)(19, \"p-dropdown\", 44);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_ng_container_23_Template_p_dropdown_ngModelChange_19_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r32 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r32.design.cathodeMaterials[1].materialVersionId = $event);\n    });\n    i0.ɵɵtemplate(20, CathodeEditorComponent_div_0_ng_container_23_ng_template_20_Template, 3, 4, \"ng-template\", 17);\n    i0.ɵɵtemplate(21, CathodeEditorComponent_div_0_ng_container_23_ng_template_21_Template, 3, 4, \"ng-template\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(22, \"div\", 5)(23, \"label\", 35);\n    i0.ɵɵi18n(24, 45);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 8)(26, \"p-inputNumber\", 37, 46);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_ng_container_23_Template_p_inputNumber_ngModelChange_26_listener($event) {\n      i0.ɵɵrestoreView(_r30);\n      const ctx_r33 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r33.design.cathodeMaterials[1].weightPercent = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(28, CathodeEditorComponent_div_0_ng_container_23_div_28_Template, 1, 0, \"div\", 28);\n    i0.ɵɵtemplate(29, CathodeEditorComponent_div_0_ng_container_23_div_29_Template, 5, 2, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(30, CathodeEditorComponent_div_0_ng_container_23_div_30_Template, 5, 3, \"div\", 47);\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const _r13 = i0.ɵɵreference(6);\n    const _r19 = i0.ɵɵreference(27);\n    i0.ɵɵnextContext();\n    const _r1 = i0.ɵɵreference(4);\n    const ctx_r5 = i0.ɵɵnextContext();\n    let tmp_16_0;\n    let tmp_20_0;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"for\", ctx_r5.controlNames.cathodeMaterial1Weight);\n    i0.ɵɵadvance(1);\n    i0.ɵɵi18nExp(1);\n    i0.ɵɵi18nApply(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", ctx_r5.controlNames.cathodeMaterial1Weight)(\"inputId\", ctx_r5.controlNames.cathodeMaterial1Weight)(\"ngModel\", ctx_r5.design.cathodeMaterials[0].weightPercent)(\"maxFractionDigits\", 2)(\"min\", 0)(\"max\", 100)(\"required\", true)(\"ngClass\", i0.ɵɵpureFunction1(35, _c32, _r13.errors == null ? null : _r13.errors[ctx_r5.fieldRequiredError]));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", _r1.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.invalid && ((_r1.errors == null ? null : _r1.errors[ctx_r5.groupSumErrorName]) || (_r13.errors == null ? null : _r13.errors[ctx_r5.fieldRequiredError])));\n    i0.ɵɵadvance(3);\n    i0.ɵɵi18nExp(2);\n    i0.ɵɵi18nApply(11);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r5.remainingBlendableMaterials)(\"ngModel\", ctx_r5.design.cathodeMaterials[1].materialId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.metrics == null ? null : ctx_r5.metrics.cathode == null ? null : ctx_r5.metrics.cathode.materialVoltageRanges == null ? null : ctx_r5.metrics.cathode.materialVoltageRanges[1]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"hidden\", !((tmp_16_0 = ctx_r5.materialMap.get(ctx_r5.design.cathodeMaterials[1].materialId)) == null ? null : tmp_16_0.length));\n    i0.ɵɵadvance(2);\n    i0.ɵɵi18nExp(2);\n    i0.ɵɵi18nApply(17);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r5.materialMap.get(ctx_r5.design.cathodeMaterials[1].materialId))(\"ngModel\", ctx_r5.design.cathodeMaterials[1].materialVersionId)(\"disabled\", ((tmp_20_0 = ctx_r5.materialMap.get(ctx_r5.design.cathodeMaterials[1].materialId)) == null ? null : tmp_20_0.length) === 1);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"for\", ctx_r5.controlNames.cathodeMaterial2Weight);\n    i0.ɵɵadvance(1);\n    i0.ɵɵi18nExp(2);\n    i0.ɵɵi18nApply(24);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"name\", ctx_r5.controlNames.cathodeMaterial2Weight)(\"inputId\", ctx_r5.controlNames.cathodeMaterial2Weight)(\"ngModel\", ctx_r5.design.cathodeMaterials[1].weightPercent)(\"maxFractionDigits\", 2)(\"min\", 0)(\"max\", 100)(\"required\", true)(\"ngClass\", i0.ɵɵpureFunction1(37, _c32, _r19.errors == null ? null : _r19.errors[ctx_r5.fieldRequiredError]));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", _r1.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r1.invalid && ((_r1.errors == null ? null : _r1.errors[ctx_r5.groupSumErrorName]) || (_r19.errors == null ? null : _r19.errors[ctx_r5.fieldRequiredError])));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r5.metrics && ctx_r5.metrics.cathode && ctx_r5.metrics.cathode.overlappingVoltageRange);\n  }\n}\nfunction CathodeEditorComponent_div_0_div_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 48);\n  }\n}\nfunction CathodeEditorComponent_div_0_div_37_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵi18n(1, 58);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵi18nExp(150);\n    i0.ɵɵi18nApply(1);\n  }\n}\nfunction CathodeEditorComponent_div_0_div_37_span_4_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵi18n(1, 59);\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CathodeEditorComponent_div_0_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"span\", 50);\n    i0.ɵɵelement(2, \"i\", 51);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(3, CathodeEditorComponent_div_0_div_37_span_3_Template, 2, 1, \"span\", 19);\n    i0.ɵɵtemplate(4, CathodeEditorComponent_div_0_div_37_span_4_Template, 2, 0, \"span\", 19);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const _r6 = i0.ɵɵreference(25);\n    const ctx_r8 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", _r6.errors == null ? null : _r6.errors[ctx_r8.qValuesMinimumError]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !(_r6.errors == null ? null : _r6.errors[ctx_r8.qValuesMinimumError]) && (_r6.errors == null ? null : _r6.errors[ctx_r8.qValuesComparisonError]));\n  }\n}\nfunction CathodeEditorComponent_div_0_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 60);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction CathodeEditorComponent_div_0_com_summary_table_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"com-summary-table\", 61);\n  }\n  if (rf & 2) {\n    const ctx_r10 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"headers\", ctx_r10.metrics.cathode.summary.headers)(\"rows\", ctx_r10.metrics.cathode.summary.rows)(\"highlightedColumns\", ctx_r10.highlightedColumns);\n  }\n}\nconst _c37 = function (a0, a1) {\n  return [a0, a1];\n};\nfunction CathodeEditorComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r37 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"p-card\");\n    i0.ɵɵelementContainerStart(3, 3, 4);\n    i0.ɵɵelementStart(5, \"div\", 5)(6, \"label\", 6);\n    i0.ɵɵi18n(7, 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 8)(9, \"p-inputSwitch\", 9);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_Template_p_inputSwitch_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r36 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r36.toggleDeveloperMode($event));\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 5)(11, \"label\", 10);\n    i0.ɵɵi18n(12, 11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 8)(14, \"p-dropdown\", 12);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_Template_p_dropdown_ngModelChange_14_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r38 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r38.design.cathodeMaterials[0].materialId = $event);\n    });\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(15, CathodeEditorComponent_div_0_div_15_Template, 2, 2, \"div\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 5)(17, \"label\", 14);\n    i0.ɵɵi18n(18, 15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 8)(20, \"p-dropdown\", 16);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_Template_p_dropdown_ngModelChange_20_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r39 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r39.design.cathodeMaterials[0].materialVersionId = $event);\n    });\n    i0.ɵɵtemplate(21, CathodeEditorComponent_div_0_ng_template_21_Template, 3, 4, \"ng-template\", 17);\n    i0.ɵɵtemplate(22, CathodeEditorComponent_div_0_ng_template_22_Template, 3, 4, \"ng-template\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(23, CathodeEditorComponent_div_0_ng_container_23_Template, 31, 39, \"ng-container\", 19);\n    i0.ɵɵelementContainerStart(24, 20, 21);\n    i0.ɵɵelementStart(26, \"div\", 5)(27, \"label\", 22);\n    i0.ɵɵi18n(28, 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(29, \"div\", 8)(30, \"p-inputNumber\", 24);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_Template_p_inputNumber_ngModelChange_30_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r40 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r40.design.cathodeQAim = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 5)(32, \"label\", 25);\n    i0.ɵɵi18n(33, 26);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"div\", 8)(35, \"p-inputNumber\", 27);\n    i0.ɵɵlistener(\"ngModelChange\", function CathodeEditorComponent_div_0_Template_p_inputNumber_ngModelChange_35_listener($event) {\n      i0.ɵɵrestoreView(_r37);\n      const ctx_r41 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r41.design.cathodeQAimFirstCharge = $event);\n    });\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(36, CathodeEditorComponent_div_0_div_36_Template, 1, 0, \"div\", 28);\n    i0.ɵɵtemplate(37, CathodeEditorComponent_div_0_div_37_Template, 5, 2, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd()();\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p-card\");\n    i0.ɵɵtemplate(39, CathodeEditorComponent_div_0_div_39_Template, 2, 0, \"div\", 30);\n    i0.ɵɵtemplate(40, CathodeEditorComponent_div_0_com_summary_table_40_Template, 1, 3, \"com-summary-table\", 31);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 2);\n    i0.ɵɵelement(42, \"com-material-chart\", 32);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r6 = i0.ɵɵreference(25);\n    const ctx_r0 = i0.ɵɵnextContext();\n    let tmp_9_0;\n    let tmp_13_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngModelGroup\", ctx_r0.formGroupNames.cathodeMaterial)(\"desiredSum\", ctx_r0.desiredSum)(\"includedControlNames\", i0.ɵɵpureFunction2(35, _c37, ctx_r0.controlNames.cathodeMaterial1Weight, ctx_r0.controlNames.cathodeMaterial2Weight))(\"skipValidation\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.design.cathodeMaterials.length > 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵi18nExp(1);\n    i0.ɵɵi18nApply(12);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.design.cathodeMaterials.length > 1 ? ctx_r0.blendableMaterials : ctx_r0.materials)(\"ngModel\", ctx_r0.design.cathodeMaterials[0].materialId);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.metrics == null ? null : ctx_r0.metrics.cathode == null ? null : ctx_r0.metrics.cathode.materialVoltageRanges == null ? null : ctx_r0.metrics.cathode.materialVoltageRanges[0]);\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassProp(\"hidden\", !((tmp_9_0 = ctx_r0.materialMap.get(ctx_r0.design.cathodeMaterials[0].materialId)) == null ? null : tmp_9_0.length));\n    i0.ɵɵadvance(2);\n    i0.ɵɵi18nExp(1);\n    i0.ɵɵi18nApply(18);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"options\", ctx_r0.materialMap.get(ctx_r0.design.cathodeMaterials[0].materialId))(\"ngModel\", ctx_r0.design.cathodeMaterials[0].materialVersionId)(\"disabled\", ((tmp_13_0 = ctx_r0.materialMap.get(ctx_r0.design.cathodeMaterials[0].materialId)) == null ? null : tmp_13_0.length) === 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.design.cathodeMaterials.length > 1);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModelGroup\", ctx_r0.formGroupNames.cathodeQValues)(\"minimumValue\", 150);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.design.cathodeQAim)(\"minFractionDigits\", 2)(\"min\", 0.001)(\"showClear\", true)(\"ngClass\", i0.ɵɵpureFunction1(38, _c32, _r6.invalid));\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngModel\", ctx_r0.design.cathodeQAimFirstCharge)(\"minFractionDigits\", 2)(\"min\", 0.001)(\"showClear\", true)(\"ngClass\", i0.ɵɵpureFunction1(40, _c32, _r6.invalid));\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r6.invalid);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", _r6.invalid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.metrics);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data\", ctx_r0.metrics == null ? null : ctx_r0.metrics.cathode == null ? null : ctx_r0.metrics.cathode.chart)(\"options\", ctx_r0.options)(\"loading\", ctx_r0.loading);\n  }\n}\nexport const validateFieldRequiredError = 'required';\nconst FORM_GROUP_NAMES = {\n  cathodeMaterial: 'cathode_formGroup',\n  cathodeQValues: 'cathode_q_values_formGroup'\n};\nconst CONTROL_NAMES = {\n  cathodeMaterial1Weight: 'cathode_material1_weight',\n  cathodeMaterial2Weight: 'cathode_material2_weight'\n};\nexport class CathodeEditorComponent {\n  constructor() {\n    // chart.js have the chart options defined as any\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    this.options = {\n      ...deepCloneDto(LINE_CHART_OPTIONS.rootOptions),\n      plugins: deepCloneDto(LINE_CHART_OPTIONS.plugins),\n      scales: deepCloneDto(LINE_CHART_OPTIONS.scales)\n    };\n    this.materialMap = new Map([]);\n    this.blendableMaterials = null;\n    this.highlightedColumns = [];\n    this.formGroupNames = FORM_GROUP_NAMES;\n    this.controlNames = CONTROL_NAMES;\n    this.desiredSum = 100;\n    this.fieldRequiredError = validateFieldRequiredError;\n    this.qValuesMinimumError = validateQValuesMinimumError;\n    this.qValuesComparisonError = validateQValuesComparisonError;\n    this.groupSumErrorName = validateGroupSumErrorActualNumber;\n    this._materials = null;\n    this._remainingBlendableMaterials = null;\n  }\n  get materials() {\n    return this._materials;\n  }\n  set materials(value) {\n    this._materials = value;\n    this.blendableMaterials = this.materials?.filter(material => material.blendable) ?? null;\n    this.materialMap = new Map();\n    this._materials?.forEach(material => {\n      this.materialMap.set(material.id, material.versions);\n    });\n  }\n  set metrics(value) {\n    if (value) {\n      this.createDefaultOptions();\n    }\n  }\n  get remainingBlendableMaterials() {\n    const result = this.blendableMaterials?.filter(m => m.id !== this.design?.cathodeMaterials[0].materialId) ?? [];\n    if (this._remainingBlendableMaterials === null || !deepEquals(result, this._remainingBlendableMaterials)) {\n      this._remainingBlendableMaterials = result;\n    }\n    return this._remainingBlendableMaterials;\n  }\n  toggleDeveloperMode(input) {\n    if (this.design && this.blendableMaterials) {\n      this.design.cathodeMaterials = input ? [{\n        materialId: this.design.cathodeMaterials[0].materialId,\n        materialVersionId: this.design.cathodeMaterials[0].materialVersionId,\n        weightPercent: 50\n      }, {\n        materialId: this.blendableMaterials[0].id,\n        materialVersionId: this.blendableMaterials[0].versions ? this.blendableMaterials[0].versions[0].id : null,\n        weightPercent: 50\n      }] : [{\n        materialId: this.design.cathodeMaterials[0].materialId,\n        materialVersionId: this.design.cathodeMaterials[0].materialVersionId,\n        weightPercent: 100\n      }];\n    }\n  }\n  ngOnInit() {\n    createDefaultOptions();\n  }\n  ngOnChanges(changes) {\n    if (changes['metrics']) {\n      this.updateHighlightedColumns();\n    }\n  }\n  createDefaultOptions() {\n    this.options.scales.x.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);\n    this.options.scales.x.title.text = $localize`:@@axisTitle.capacityCathode: Capacity / mAh/g(CAM)`;\n    this.options.scales.y.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);\n    this.options.scales.y.title.text = $localize`:@@axisTitle.voltage: Voltage vs. Li / V`;\n    this.options.scales.y.min = 3;\n    if (this.metrics) {\n      const box = this.metrics?.balancing.chartAnnotations;\n      box[0].yMin = 2.2;\n      box[0].yMax = 4.2;\n      box[0].xMin = 50;\n      // box[0].rotation = 90;\n      box[0].label.rotation = 0;\n      box[0].label.content = 'haker bace';\n      console.log('@@@box', box);\n      this.options.plugins.annotation = {\n        annotations: this.metrics?.balancing.chartAnnotations\n      };\n    } else {\n      console.log('nqq metrikcs bace');\n    }\n  }\n  updateHighlightedColumns() {\n    const indexOfBlendColumn = this.metrics?.cathode.summary.headers.indexOf('Blend');\n    const indexOfModifiedValueByTarget = this.metrics?.cathode.summary.headers.indexOf('Modified value by target');\n    if ((this.design?.cathodeMaterials ?? []).length > 1) {\n      this.highlightedColumns = [];\n      if (indexOfBlendColumn !== -1 && indexOfBlendColumn !== undefined) {\n        this.highlightedColumns.push(indexOfBlendColumn);\n      }\n    } else {\n      this.highlightedColumns = [];\n      if (indexOfModifiedValueByTarget !== -1 && indexOfModifiedValueByTarget !== undefined) {\n        this.highlightedColumns.push(indexOfModifiedValueByTarget);\n      }\n    }\n  }\n}\nCathodeEditorComponent.ɵfac = function CathodeEditorComponent_Factory(t) {\n  return new (t || CathodeEditorComponent)();\n};\nCathodeEditorComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: CathodeEditorComponent,\n  selectors: [[\"com-cathode-editor\"]],\n  inputs: {\n    materials: \"materials\",\n    loading: \"loading\",\n    design: \"design\",\n    metrics: \"metrics\"\n  },\n  features: [i0.ɵɵProvidersFeature([], [{\n    provide: ControlContainer,\n    useExisting: NgModelGroup\n  }]), i0.ɵɵNgOnChangesFeature],\n  decls: 1,\n  vars: 1,\n  consts: function () {\n    let i18n_0;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_developerMode_checkbox_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__1 = goog.getMsg(\" Entwicklermodus \");\n      i18n_0 = MSG_EXTERNAL_chemicals_common_developerMode_checkbox_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__1;\n    } else {\n      i18n_0 = $localize`:@@chemicals.common.developerMode.checkbox.label: Entwicklermodus `;\n    }\n    let i18n_2;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_material_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__3 = goog.getMsg(\" Material {$interpolation} \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ 1 }}\"\n        }\n      });\n      i18n_2 = MSG_EXTERNAL_chemicals_common_material_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__3;\n    } else {\n      i18n_2 = $localize`:@@chemicals.common.material.input.label: Material ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION: `;\n    }\n    let i18n_4;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_material_version_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__5 = goog.getMsg(\" Version Material {$interpolation} \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ 1 }}\"\n        }\n      });\n      i18n_4 = MSG_EXTERNAL_chemicals_common_material_version_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__5;\n    } else {\n      i18n_4 = $localize`:@@chemicals.common.material.version.input.label: Version Material ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION: `;\n    }\n    let i18n_6;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_qAim_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__7 = goog.getMsg(\" Ziel Qrev C/10 \");\n      i18n_6 = MSG_EXTERNAL_chemicals_common_qAim_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__7;\n    } else {\n      i18n_6 = $localize`:@@chemicals.common.qAim.input.label: Ziel Qrev C/10 `;\n    }\n    let i18n_8;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_qAim1_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__9 = goog.getMsg(\" Q 1st Aim \");\n      i18n_8 = MSG_EXTERNAL_chemicals_common_qAim1_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS__9;\n    } else {\n      i18n_8 = $localize`:@@chemicals.common.qAim1.input.label: Q 1st Aim `;\n    }\n    let i18n_10;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_voltageRange_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___11 = goog.getMsg(\" Spannungsbereich: {$interpolation}V - {$interpolation_1}V \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\",\n        \"interpolation_1\": \"\\uFFFD1\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ metrics?.cathode?.materialVoltageRanges?.[0]?.[0] }}\",\n          \"interpolation_1\": \"{{ metrics?.cathode?.materialVoltageRanges?.[0]?.[1] }}\"\n        }\n      });\n      i18n_10 = MSG_EXTERNAL_chemicals_common_voltageRange_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___11;\n    } else {\n      i18n_10 = $localize`:@@chemicals.common.voltageRange.label: Spannungsbereich: ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION:V - ${\"\\uFFFD1\\uFFFD\"}:INTERPOLATION_1:V `;\n    }\n    let i18n_12;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_material_ratio_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___13 = goog.getMsg(\" Anteil Material {$interpolation} \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ 1 }}\"\n        }\n      });\n      i18n_12 = MSG_EXTERNAL_chemicals_common_material_ratio_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___13;\n    } else {\n      i18n_12 = $localize`:@@chemicals.common.material.ratio.input.label: Anteil Material ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION: `;\n    }\n    let i18n_14;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_material_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___15 = goog.getMsg(\" Material {$interpolation} \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ 2 }}\"\n        }\n      });\n      i18n_14 = MSG_EXTERNAL_chemicals_common_material_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___15;\n    } else {\n      i18n_14 = $localize`:@@chemicals.common.material.input.label: Material ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION: `;\n    }\n    let i18n_16;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_material_version_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___17 = goog.getMsg(\" Version Material {$interpolation} \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ 2 }}\"\n        }\n      });\n      i18n_16 = MSG_EXTERNAL_chemicals_common_material_version_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___17;\n    } else {\n      i18n_16 = $localize`:@@chemicals.common.material.version.input.label: Version Material ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION: `;\n    }\n    let i18n_18;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_material_ratio_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___19 = goog.getMsg(\" Anteil Material {$interpolation} \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ 2 }}\"\n        }\n      });\n      i18n_18 = MSG_EXTERNAL_chemicals_common_material_ratio_input_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS___19;\n    } else {\n      i18n_18 = $localize`:@@chemicals.common.material.ratio.input.label: Anteil Material ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION: `;\n    }\n    let i18n_20;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_swelling_fieldIsRequired$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS_____21 = goog.getMsg(\" Feld ist erforderlich. \");\n      i18n_20 = MSG_EXTERNAL_swelling_fieldIsRequired$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS_____21;\n    } else {\n      i18n_20 = $localize`:@@swelling.fieldIsRequired: Feld ist erforderlich. `;\n    }\n    let i18n_22;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_materials_percentages_error$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS_____23 = goog.getMsg(\"Die Summe der Prozents\\u00E4tze muss 100 ergeben\");\n      i18n_22 = MSG_EXTERNAL_materials_percentages_error$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS_____23;\n    } else {\n      i18n_22 = $localize`:@@materials.percentages.error:Die Summe der Prozentsätze muss 100 ergeben`;\n    }\n    let i18n_24;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_voltageRange_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS____25 = goog.getMsg(\" Spannungsbereich: {$interpolation}V - {$interpolation_1}V \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\",\n        \"interpolation_1\": \"\\uFFFD1\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ metrics?.cathode?.materialVoltageRanges?.[1]?.[0] }}\",\n          \"interpolation_1\": \"{{ metrics?.cathode?.materialVoltageRanges?.[1]?.[1] }}\"\n        }\n      });\n      i18n_24 = MSG_EXTERNAL_chemicals_common_voltageRange_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS____25;\n    } else {\n      i18n_24 = $localize`:@@chemicals.common.voltageRange.label: Spannungsbereich: ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION:V - ${\"\\uFFFD1\\uFFFD\"}:INTERPOLATION_1:V `;\n    }\n    let i18n_26;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_swelling_fieldIsRequired$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS_____27 = goog.getMsg(\" Feld ist erforderlich. \");\n      i18n_26 = MSG_EXTERNAL_swelling_fieldIsRequired$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS_____27;\n    } else {\n      i18n_26 = $localize`:@@swelling.fieldIsRequired: Feld ist erforderlich. `;\n    }\n    let i18n_28;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_materials_percentages_error$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS_____29 = goog.getMsg(\"Die Summe der Prozents\\u00E4tze muss 100 ergeben\");\n      i18n_28 = MSG_EXTERNAL_materials_percentages_error$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS_____29;\n    } else {\n      i18n_28 = $localize`:@@materials.percentages.error:Die Summe der Prozentsätze muss 100 ergeben`;\n    }\n    let i18n_30;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_chemicals_common_overlappingVoltageRange_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS____31 = goog.getMsg(\" \\u00DCberlappenden Spannungsbereich \\u00FCberblenden \");\n      i18n_30 = MSG_EXTERNAL_chemicals_common_overlappingVoltageRange_label$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS____31;\n    } else {\n      i18n_30 = $localize`:@@chemicals.common.overlappingVoltageRange.label: Überlappenden Spannungsbereich überblenden `;\n    }\n    let i18n_33;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_qValues_minimumValue_error$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS____34 = goog.getMsg(\" Sowohl Ziel Qrev C/10 als auch Ziel Q1st C/10 m\\u00FCssen mindestens {$interpolation} mAh/g betragen \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ 150 }}\"\n        }\n      });\n      i18n_33 = MSG_EXTERNAL_qValues_minimumValue_error$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS____34;\n    } else {\n      i18n_33 = $localize`:@@qValues.minimumValue.error: Sowohl Ziel Qrev C/10 als auch Ziel Q1st C/10 müssen mindestens ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION: mAh/g betragen `;\n    }\n    let i18n_35;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_qValues_comparison_error$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS____36 = goog.getMsg(\" Ziel Q1st C/10 muss gr\\u00F6\\u00DFer sein als Ziel Qrev C/10 \");\n      i18n_35 = MSG_EXTERNAL_qValues_comparison_error$$SRC_APP_CELL_DESIGN_PAGE_CATHODE_EDITOR_CATHODE_EDITOR_COMPONENT_TS____36;\n    } else {\n      i18n_35 = $localize`:@@qValues.comparison.error: Ziel Q1st C/10 muss größer sein als Ziel Qrev C/10 `;\n    }\n    return [[\"class\", \"grid align-items-stretch\", 4, \"ngIf\"], [1, \"grid\", \"align-items-stretch\"], [1, \"col-12\", \"lg:col-6\"], [\"comValidateGroupSumDirective\", \"\", 3, \"ngModelGroup\", \"desiredSum\", \"includedControlNames\", \"skipValidation\"], [\"cathodeMaterialFormGroup\", \"ngModelGroup\"], [1, \"field\", \"grid\"], [\"for\", \"cathode_developer_mode\", 1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], i18n_0, [1, \"col-12\", \"xl:col-9\", \"p-fluid\"], [\"name\", \"cathode_developer_mode\", \"inputId\", \"cathode_developer_mode\", 3, \"ngModel\", \"ngModelChange\"], [\"for\", \"cathode_material1_id\", 1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], i18n_2, [\"name\", \"cathode_material1_id\", \"inputId\", \"cathode_material1_id\", \"optionLabel\", \"name\", \"optionValue\", \"id\", 3, \"options\", \"ngModel\", \"ngModelChange\"], [\"class\", \"text-sm text-gray-300 mt-1\", 4, \"ngIf\"], [\"for\", \"cathode_material1_version_id\", 1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], i18n_4, [\"name\", \"cathode_material1_version_id\", \"inputId\", \"cathode_material1_version_id\", \"optionLabel\", \"description\", \"optionValue\", \"id\", 3, \"options\", \"ngModel\", \"disabled\", \"ngModelChange\"], [\"pTemplate\", \"selectedItem\"], [\"pTemplate\", \"item\"], [4, \"ngIf\"], [\"comValidateQValuesDirective\", \"\", \"qRevControlName\", \"cathode_q_aim\", \"q1stControlName\", \"cathode_q_aim_1\", 3, \"ngModelGroup\", \"minimumValue\"], [\"cathodeQValuesFormGroup\", \"ngModelGroup\"], [\"for\", \"cathode_q_aim\", 1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], i18n_6, [\"comCustomizeInput\", \"\", \"name\", \"cathode_q_aim\", \"inputId\", \"cathode_q_aim\", \"mode\", \"decimal\", \"suffix\", \" mAh/g\", 3, \"ngModel\", \"minFractionDigits\", \"min\", \"showClear\", \"ngClass\", \"ngModelChange\"], [\"for\", \"cathode_q_aim_1\", 1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], i18n_8, [\"comCustomizeInput\", \"\", \"name\", \"cathode_q_aim_1\", \"inputId\", \"cathode_q_aim_1\", \"mode\", \"decimal\", \"suffix\", \" mAh/g\", 3, \"ngModel\", \"minFractionDigits\", \"min\", \"showClear\", \"ngClass\", \"ngModelChange\"], [\"class\", \"col-12 mb-2 xl:col-3 xl:mb-0\", 4, \"ngIf\"], [\"class\", \"com-text-error col-12 xl:col-9 p-fluid mt-1\", 4, \"ngIf\"], [\"class\", \"flex justify-content-center\", 4, \"ngIf\"], [3, \"headers\", \"rows\", \"highlightedColumns\", 4, \"ngIf\"], [3, \"data\", \"options\", \"loading\"], [1, \"text-sm\", \"text-gray-300\", \"mt-1\"], i18n_10, [1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\", 3, \"for\"], i18n_12, [\"comCustomizeInput\", \"\", \"mode\", \"decimal\", \"suffix\", \" w%\", 3, \"name\", \"inputId\", \"ngModel\", \"maxFractionDigits\", \"min\", \"max\", \"required\", \"ngClass\", \"ngModelChange\"], [\"cathodeMaterial1WeightField\", \"ngModel\"], [\"for\", \"cathode_material2_id\", 1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], i18n_14, [\"name\", \"cathode_material2_id\", \"inputId\", \"cathode_material2_id\", \"optionLabel\", \"name\", \"optionValue\", \"id\", 3, \"options\", \"ngModel\", \"ngModelChange\"], [\"for\", \"cathode_material2_version_id\", 1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], i18n_16, [\"name\", \"cathode_material2_version_id\", \"inputId\", \"cathode_material2_version_id\", \"optionLabel\", \"description\", \"optionValue\", \"id\", 3, \"options\", \"ngModel\", \"disabled\", \"ngModelChange\"], i18n_18, [\"cathodeMaterial2WeightField\", \"ngModel\"], [\"class\", \"field grid\", 4, \"ngIf\"], [1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], [1, \"com-text-error\", \"col-12\", \"xl:col-9\", \"p-fluid\", \"mt-1\"], [1, \"mr-2\"], [1, \"pi\", \"pi-times-circle\"], i18n_20, i18n_22, i18n_24, i18n_26, i18n_28, i18n_30, i18n_33, i18n_35, [1, \"flex\", \"justify-content-center\"], [3, \"headers\", \"rows\", \"highlightedColumns\"]];\n  },\n  template: function CathodeEditorComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, CathodeEditorComponent_div_0_Template, 43, 42, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.materials && ctx.blendableMaterials && ctx.design);\n    }\n  },\n  dependencies: [i1.ValidateGroupSumDirective, i2.ValidateQValuesDirective, i3.NgClass, i3.NgIf, i4.NgControlStatus, i4.NgControlStatusGroup, i4.RequiredValidator, i4.NgModel, i4.NgModelGroup, i5.CustomizePrimeInputDirective, i6.PrimeTemplate, i7.Dropdown, i8.InputNumber, i9.Card, i10.InputSwitch, i11.ProgressSpinner, i12.SummaryTableComponent, i13.MaterialChartComponent, i3.DatePipe],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": "AACA,SAASA,gBAAgB,EAAEC,YAAY,QAAQ,gBAAgB;AAE/D,SAASC,kBAAkB,QAAQ,YAAY;AAI/C,SAASC,UAAU,QAAQ,wBAAwB;AACnD,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SACIC,2BAA2B,EAC3BC,8BAA8B,QAC3B,kEAAkE;AACzE,SAASC,iCAAiC,QAAQ,mEAAmE;;;;;;;;;;;;;;;;;ICiC7FC,+BAIC;IAJDA,gBAIC;IAGDA,iBAAM;;;;IAANA,eAAM;IAANA,uQAAM;IAANA,iBAAM;;;;;IA0BEA,2BAAK;IAAAA,YAAqE;;IAAAA,iBAAM;;;;IAA3EA,eAAqE;IAArEA,qHAAqE;;;;;IAG1EA,2BAAK;IAAAA,YAAqD;;IAAAA,iBAAM;;;;IAA3DA,eAAqD;IAArDA,qGAAqD;;;;;IAiClEA,0BAAyF;;;;;IAMrFA,4BAGC;IAHDA,gBAGC;IAEDA,iBAAO;;;;;IACPA,4BAGK;IAHLA,gBAGK;IAA2CA,iBAC/C;;;;;IAfLA,+BAGC;IACsBA,wBAAkC;IAAAA,iBAAO;IAC5DA,sGAKO;IACPA,sGAIC;IACLA,iBAAM;;;;;;;;IAVGA,eAAqG;IAArGA,6GAAqG;IAMrGA,eAAuH;IAAvHA,qKAAuH;;;;;IAuB5HA,+BAIC;IAJDA,gBAIC;IAGDA,iBAAM;;;;IAANA,eAAM;IAANA,4QAAM;IAANA,iBAAM;;;;;IA0BEA,2BAAK;IAAAA,YAAqE;;IAAAA,iBAAM;;;;IAA3EA,eAAqE;IAArEA,qHAAqE;;;;;IAG1EA,2BAAK;IAAAA,YAAqD;;IAAAA,iBAAM;;;;IAA3DA,eAAqD;IAArDA,qGAAqD;;;;;IAgCtEA,0BAAyF;;;;;IAMrFA,4BAGC;IAHDA,gBAGC;IAEDA,iBAAO;;;;;IACPA,4BAGK;IAHLA,gBAGK;IAA2CA,iBAC/C;;;;;IAfLA,+BAGC;IACsBA,wBAAkC;IAAAA,iBAAO;IAC5DA,uGAKO;IACPA,uGAIC;IACLA,iBAAM;;;;;;;;IAVGA,eAAqG;IAArGA,6GAAqG;IAMrGA,eAAuH;IAAvHA,qKAAuH;;;;;IAOpIA,8BAGC;IACGA,gBAIC;IAEDA,iBAAQ;IACRA,8BAAqC;IACjCA,YAEJ;IAAAA,iBAAM;;;;IATFA,eAA2C;IAA3CA,iEAA2C;IAO3CA,eAEJ;IAFIA,gJAEJ;;;;;;;;;;;IArKRA,6BAAyD;IACrDA,8BAAwB;IACpBA,gBAIC;IAEDA,iBAAQ;IACRA,8BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,+CAAwB,CAAC,yBACjE;IAAA,EADiF;IAWzDA,iBAAgB;IAErBA,8FAAyF;IACzFA,8FAgBM;IACVA,iBAAM;IACNA,8BAAwB;IACpBA,iBAIC;IAEDA,iBAAQ;IACRA,+BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,+CAAwB,CAAC,sBACjE;IAAA,EAD8E;IAGtDA,iBAAa;IACdA,gGAOM;IACVA,iBAAM;IAGVA,+BAGC;IACGA,iBAIC;IAEDA,iBAAQ;IACRA,+BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,+CAAwB,CAAC,6BACjE;IAAA,EADqF;IAK1DA,gHAEc;IACdA,gHAEc;IAAAA,iBACjB;IAITA,+BAAwB;IACpBA,iBAIC;IAEDA,iBAAQ;IACRA,+BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,+CAAwB,CAAC,yBACjE;IAAA,EADiF;IAWzDA,iBAAgB;IAErBA,gGAAyF;IACzFA,gGAgBM;IACVA,iBAAM;IAENA,gGAeM;IACVA,0BAAe;;;;;;;;;;IApKHA,eAA2C;IAA3CA,gEAA2C;IAK/CA,eAAQ;IAARA,eAAQ;IAARA,iBAAQ;IAIAA,eAA4C;IAA5CA,iEAA4C;IAe9CA,eAAsC;IAAtCA,kCAAsC;IAEvCA,eAA4J;IAA5JA,mLAA4J;IAwBjKA,eAAQ;IAARA,eAAQ;IAARA,kBAAQ;IAKAA,eAAuC;IAAvCA,4DAAuC;IAMtCA,eAAkD;IAAlDA,4MAAkD;IAY3DA,eAAgF;IAAhFA,+IAAgF;IAQhFA,eAAQ;IAARA,eAAQ;IAARA,kBAAQ;IAKAA,eAAmE;IAAnEA,8FAAmE;IAkBvEA,eAA2C;IAA3CA,gEAA2C;IAK/CA,eAAQ;IAARA,eAAQ;IAARA,kBAAQ;IAIAA,eAA4C;IAA5CA,iEAA4C;IAe9CA,eAAsC;IAAtCA,kCAAsC;IAEvCA,eAA4J;IAA5JA,mLAA4J;IAmBhKA,eAA2E;IAA3EA,iHAA2E;;;;;IAyE5EA,0BAAwF;;;;;IAMpFA,4BAGC;IAHDA,gBAGC;IAEDA,iBAAO;;;IAAPA,eAAO;IAAPA,iBAAO;IAAPA,iBAAO;;;;;IACPA,4BAGC;IAHDA,gBAGC;IAEDA,iBAAO;;;;;IAhBXA,+BAGC;IACsBA,wBAAkC;IAAAA,iBAAO;IAC5DA,uFAKO;IACPA,uFAKO;IACXA,iBAAM;;;;;;IAXGA,eAA2D;IAA3DA,yFAA2D;IAM3DA,eAAwH;IAAxHA,uKAAwH;;;;;IAY7IA,+BAAyD;IACrDA,oCAAuC;IAC3CA,iBAAM;;;;;IAENA,wCAKqB;;;;IAHjBA,iEAA2C;;;;;;;;;IAxV3DA,8BAAwF;IAG5EA,mCAOC;IACGA,8BAAwB;IACpBA,eAIC;IAEDA,iBAAQ;IACRA,8BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAiBA,kDAA2B;IAAA,EAAC;IAChDA,iBAAgB;IAIzBA,+BAAwB;IACpBA,iBAIC;IAEDA,iBAAQ;IACRA,+BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,+CAAwB,CAAC,sBAC7D;IAAA,EAD0E;IAGtDA,iBAAa;IACdA,gFAOM;IACVA,iBAAM;IAGVA,+BAGC;IACGA,iBAIC;IAEDA,iBAAQ;IACRA,+BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,+CAAwB,CAAC,6BAC7D;IAAA,EADiF;IAK1DA,gGAEc;IACdA,gGAEc;IAClBA,iBAAa;IAIrBA,oGAuKe;IACfA,sCAOC;IACGA,+BAAwB;IACpBA,iBAIC;IAEDA,iBAAQ;IACRA,+BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,mDACxC;IAAA,EAD2D;IASnCA,iBAAgB;IAGzBA,+BAAwB;IACpBA,iBAIC;IAEDA,iBAAQ;IACRA,+BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,8DACxC;IAAA,EADsE;IAS9CA,iBAAgB;IAErBA,gFAAwF;IACxFA,gFAiBM;IACVA,iBAAM;IACVA,0BAAe;IAEvBA,iBAAS;IAETA,+BAAQ;IACJA,gFAEM;IAENA,4GAKqB;IACzBA,iBAAS;IAGbA,+BAA6B;IACzBA,0CAA+F;IACnGA,iBAAM;;;;;;;IA7VMA,eAA+C;IAA/CA,oEAA+C;IAmBnCA,eAA8C;IAA9CA,mEAA8C;IAatDA,eAAQ;IAARA,eAAQ;IAARA,kBAAQ;IAKAA,eAA+E;IAA/EA,kHAA+E;IAM9EA,eAAkD;IAAlDA,4MAAkD;IAY3DA,eAAgF;IAAhFA,6IAAgF;IAQhFA,eAAQ;IAARA,eAAQ;IAARA,kBAAQ;IAKAA,eAAmE;IAAnEA,8FAAmE;IAgBhEA,eAAwC;IAAxCA,gEAAwC;IAyKnDA,eAA8C;IAA9CA,mEAA8C;IAoBlCA,eAAgC;IAAhCA,mDAAgC;IAyBhCA,eAA2C;IAA3CA,8DAA2C;IAW7CA,eAAqC;IAArCA,kCAAqC;IAEtCA,eAAqC;IAArCA,kCAAqC;IAuBhDA,eAAa;IAAbA,qCAAa;IAKdA,eAAa;IAAbA,qCAAa;IASFA,eAAgC;IAAhCA,2HAAgC;;;ADjV5D,OAAO,MAAMC,0BAA0B,GAAG,UAAU;AAEpD,MAAMC,gBAAgB,GAAG;EACrBC,eAAe,EAAE,mBAAmB;EACpCC,cAAc,EAAE;CACV;AAEV,MAAMC,aAAa,GAAG;EAClBC,sBAAsB,EAAE,0BAA0B;EAClDC,sBAAsB,EAAE;CAClB;AAUV,OAAM,MAAOC,sBAAsB;EARnCC;IAqCI;IACA;IACO,YAAO,GAAQ;MAClB,GAAGb,YAAY,CAACF,kBAAkB,CAACgB,WAAW,CAAC;MAC/CC,OAAO,EAAEf,YAAY,CAACF,kBAAkB,CAACiB,OAAO,CAAC;MACjDC,MAAM,EAAEhB,YAAY,CAACF,kBAAkB,CAACkB,MAAM;KACjD;IAEM,gBAAW,GAAwC,IAAIC,GAAG,CAAC,EAAE,CAAC;IAE9D,uBAAkB,GAAsB,IAAI;IAE5C,uBAAkB,GAAa,EAAE;IAYjC,mBAAc,GAAGX,gBAAgB;IACjC,iBAAY,GAAGG,aAAa;IAEnB,eAAU,GAAG,GAAG;IAChB,uBAAkB,GAAGJ,0BAA0B;IAC/C,wBAAmB,GAAGJ,2BAA2B;IACjD,2BAAsB,GAAGC,8BAA8B;IACvD,sBAAiB,GAAGC,iCAAiC;IAE7D,eAAU,GAAsB,IAAI;IAEpC,iCAA4B,GAAsB,IAAI;;EA/D9D,IACWe,SAAS;IAChB,OAAO,IAAI,CAACC,UAAU;EAC1B;EAEA,IAAWD,SAAS,CAACE,KAAwB;IACzC,IAAI,CAACD,UAAU,GAAGC,KAAK;IACvB,IAAI,CAACC,kBAAkB,GAAG,IAAI,CAACH,SAAS,EAAEI,MAAM,CAAEC,QAAQ,IAAKA,QAAQ,CAACC,SAAS,CAAC,IAAI,IAAI;IAC1F,IAAI,CAACC,WAAW,GAAG,IAAIR,GAAG,EAAE;IAE5B,IAAI,CAACE,UAAU,EAAEO,OAAO,CAAEH,QAAQ,IAAI;MAClC,IAAI,CAACE,WAAW,CAACE,GAAG,CAACJ,QAAQ,CAACK,EAAE,EAAEL,QAAQ,CAACM,QAAQ,CAAC;IACxD,CAAC,CAAC;EACN;EAQA,IACWC,OAAO,CAACV,KAAoC;IACnD,IAAIA,KAAK,EAAE;MACP,IAAI,CAACW,oBAAoB,EAAE;;EAEnC;EAgBA,IAAWC,2BAA2B;IAClC,MAAMC,MAAM,GACR,IAAI,CAACZ,kBAAkB,EAAEC,MAAM,CAAEY,CAAC,IAAKA,CAAC,CAACN,EAAE,KAAK,IAAI,CAACO,MAAM,EAAEC,gBAAgB,CAAC,CAAC,CAAC,CAACC,UAAU,CAAC,IAAI,EAAE;IACtG,IAAI,IAAI,CAACC,4BAA4B,KAAK,IAAI,IAAI,CAACvC,UAAU,CAACkC,MAAM,EAAE,IAAI,CAACK,4BAA4B,CAAC,EAAE;MACtG,IAAI,CAACA,4BAA4B,GAAGL,MAAM;;IAG9C,OAAO,IAAI,CAACK,4BAA4B;EAC5C;EAeOC,mBAAmB,CAACC,KAAc;IACrC,IAAI,IAAI,CAACL,MAAM,IAAI,IAAI,CAACd,kBAAkB,EAAE;MACxC,IAAI,CAACc,MAAM,CAACC,gBAAgB,GAAGI,KAAK,GAC9B,CACI;QACIH,UAAU,EAAE,IAAI,CAACF,MAAM,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAACC,UAAU;QACtDI,iBAAiB,EAAE,IAAI,CAACN,MAAM,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAACK,iBAAiB;QACpEC,aAAa,EAAE;OAClB,EACD;QACIL,UAAU,EAAE,IAAI,CAAChB,kBAAkB,CAAC,CAAC,CAAC,CAACO,EAAE;QACzCa,iBAAiB,EAAE,IAAI,CAACpB,kBAAkB,CAAC,CAAC,CAAC,CAACQ,QAAQ,GAChD,IAAI,CAACR,kBAAkB,CAAC,CAAC,CAAC,CAACQ,QAAQ,CAAC,CAAC,CAAC,CAACD,EAAE,GACzC,IAAI;QACVc,aAAa,EAAE;OAClB,CACJ,GACD,CACI;QACIL,UAAU,EAAE,IAAI,CAACF,MAAM,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAACC,UAAU;QACtDI,iBAAiB,EAAE,IAAI,CAACN,MAAM,CAACC,gBAAgB,CAAC,CAAC,CAAC,CAACK,iBAAiB;QACpEC,aAAa,EAAE;OAClB,CACJ;;EAEf;EAEOC,QAAQ;IACnBZ,oBAAoB,EAAE;EAClB;EAEOa,WAAW,CAACC,OAAsB;IACrC,IAAIA,OAAO,CAAC,SAAS,CAAC,EAAE;MACpB,IAAI,CAACC,wBAAwB,EAAE;;EAEvC;EAEOf,oBAAoB;IACvB,IAAI,CAACgB,OAAO,CAAC/B,MAAM,CAACgC,CAAC,CAACC,KAAK,GAAGjD,YAAY,CAACF,kBAAkB,CAACoD,SAAS,CAAC;IACxE,IAAI,CAACH,OAAO,CAAC/B,MAAM,CAACgC,CAAC,CAACC,KAAK,CAACE,IAAI,GAAGC,SAAS,qDAAqD;IAEjG,IAAI,CAACL,OAAO,CAAC/B,MAAM,CAACqC,CAAC,CAACJ,KAAK,GAAGjD,YAAY,CAACF,kBAAkB,CAACoD,SAAS,CAAC;IACxE,IAAI,CAACH,OAAO,CAAC/B,MAAM,CAACqC,CAAC,CAACJ,KAAK,CAACE,IAAI,GAAGC,SAAS,0CAA0C;IAEtF,IAAI,CAACL,OAAO,CAAC/B,MAAM,CAACqC,CAAC,CAACC,GAAG,GAAG,CAAC;IAE7B,IAAI,IAAI,CAACxB,OAAO,EAAE;MACd,MAAMyB,GAAG,GAAG,IAAI,CAACzB,OAAO,EAAE0B,SAAS,CAACC,gBAAgB;MACpDF,GAAG,CAAC,CAAC,CAAC,CAACG,IAAI,GAAG,GAAG;MACjBH,GAAG,CAAC,CAAC,CAAC,CAACI,IAAI,GAAG,GAAG;MACjBJ,GAAG,CAAC,CAAC,CAAC,CAACK,IAAI,GAAG,EAAE;MAChB;MACAL,GAAG,CAAC,CAAC,CAAC,CAACM,KAAK,CAACC,QAAQ,GAAG,CAAC;MACzBP,GAAG,CAAC,CAAC,CAAC,CAACM,KAAK,CAACE,OAAO,GAAG,YAAY;MACnCC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAEV,GAAG,CAAC;MAC1B,IAAI,CAACR,OAAO,CAAChC,OAAO,CAACmD,UAAU,GAAG;QAC9BC,WAAW,EAAE,IAAI,CAACrC,OAAO,EAAE0B,SAAS,CAACC;OACxC;KACJ,MAAM;MACHO,OAAO,CAACC,GAAG,CAAC,mBAAmB,CAAC;;EAExC;EAEQnB,wBAAwB;IAC5B,MAAMsB,kBAAkB,GAAG,IAAI,CAACtC,OAAO,EAAEuC,OAAO,CAACC,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,OAAO,CAAC;IACjF,MAAMC,4BAA4B,GAAG,IAAI,CAAC3C,OAAO,EAAEuC,OAAO,CAACC,OAAO,CAACC,OAAO,CAACC,OAAO,CAAC,0BAA0B,CAAC;IAC9G,IAAI,CAAC,IAAI,CAACrC,MAAM,EAAEC,gBAAgB,IAAI,EAAE,EAAEsC,MAAM,GAAG,CAAC,EAAE;MAClD,IAAI,CAACC,kBAAkB,GAAG,EAAE;MAC5B,IAAIP,kBAAkB,KAAK,CAAC,CAAC,IAAIA,kBAAkB,KAAKQ,SAAS,EAAE;QAC/D,IAAI,CAACD,kBAAkB,CAACE,IAAI,CAACT,kBAAkB,CAAC;;KAEvD,MAAM;MACH,IAAI,CAACO,kBAAkB,GAAG,EAAE;MAC5B,IAAIF,4BAA4B,KAAK,CAAC,CAAC,IAAIA,4BAA4B,KAAKG,SAAS,EAAE;QACnF,IAAI,CAACD,kBAAkB,CAACE,IAAI,CAACJ,4BAA4B,CAAC;;;EAGtE;;AA/IS7D,sBAAsB;mBAAtBA,sBAAsB;AAAA;AAAtBA,sBAAsB;QAAtBA,sBAAsB;EAAAkE;EAAAC;IAAA7D;IAAA8D;IAAA7C;IAAAL;EAAA;EAAAmD,qCAFhB,CAAC;IAAEC,OAAO,EAAEtF,gBAAgB;IAAEuF,WAAW,EAAEtF;EAAY,CAAE,CAAC;EAAAuF;EAAAC;EAAAC;IAAA;IAAA;;;;;;;eCjBxDlC,6EAED;;;;;;;;;;;;;;;;eAgBCA,+DACY,eAAO,kBACpB;;;;;;;;;;;;;;;;eA6BCA,+EACoB,eAAO,kBAC5B;;;;;;;;;;eA0MKA,gEAED;;;;;;;;;;eAuBCA,4DAED;;;;;;;;;;;;;;;;;;gBAtPCA,sEACqB,eAAuD,sBACzE,eAAuD,qBAC3D;;;;;;;;;;;;;;;;gBAyCCA,4EACmB,eAAO,kBAC3B;;;;;;;;;;;;;;;;gBA2CCA,+DACY,eAAO,kBACpB;;;;;;;;;;;;;;;;gBA6BCA,+EACoB,eAAO,kBAC5B;;;;;;;;;;;;;;;;gBA0BCA,4EACmB,eAAO,kBAC3B;;;;;;;;;;gBA5EKA,+DAED;;;;;;;;;;gBAIKA,qFAA2C;;;;;;;;;;;;;;;;;;gBAyB/CA,sEACqB,eAAuD,sBACzE,eAAuD,qBAC3D;;;;;;;;;;gBAsECA,+DAED;;;;;;;;;;gBAIKA,qFAA2C;;;;;;;;;;gBAanDA,yGAED;;;;;;;;;;;;;;;;gBAyEKA,2GACmE,eAAS,iCAC7E;;;;;;;;;;gBAICA,2FAED;;;;;;MA1U5BhD,yEAkWM;;;MAlWAA,4EAA+C", "names": ["ControlContainer", "NgModelGroup", "LINE_CHART_OPTIONS", "deepEquals", "deepCloneDto", "validateQValuesMinimumError", "validateQValuesComparisonError", "validateGroupSumErrorActualNumber", "i0", "validateFieldRequiredError", "FORM_GROUP_NAMES", "cathodeMaterial", "cathodeQValues", "CONTROL_NAMES", "cathodeMaterial1Weight", "cathodeMaterial2Weight", "CathodeEditorComponent", "constructor", "rootOptions", "plugins", "scales", "Map", "materials", "_materials", "value", "blendableMaterials", "filter", "material", "blendable", "materialMap", "for<PERSON>ach", "set", "id", "versions", "metrics", "createDefaultOptions", "remainingBlendableMaterials", "result", "m", "design", "cathodeMaterials", "materialId", "_remainingBlendableMaterials", "toggleDeveloperMode", "input", "materialVersionId", "weightPercent", "ngOnInit", "ngOnChanges", "changes", "updateHighlightedColumns", "options", "x", "title", "axisTitle", "text", "$localize", "y", "min", "box", "balancing", "chartAnnotations", "yMin", "yMax", "xMin", "label", "rotation", "content", "console", "log", "annotation", "annotations", "indexOfBlendColumn", "cathode", "summary", "headers", "indexOf", "indexOfModifiedValueByTarget", "length", "highlightedColumns", "undefined", "push", "selectors", "inputs", "loading", "features", "provide", "useExisting", "decls", "vars", "consts"], "sourceRoot": "", "sources": ["D:\\Repos\\cellforce\\dmgmt-cell-o-mat-2.0-frontend\\src\\app\\cell-design-page\\cathode-editor\\cathode-editor.component.ts", "D:\\Repos\\cellforce\\dmgmt-cell-o-mat-2.0-frontend\\src\\app\\cell-design-page\\cathode-editor\\cathode-editor.component.html"], "sourcesContent": ["import { Component, Input, OnChanges, OnInit, SimpleChanges } from '@angular/core';\r\nimport { ControlContainer, NgModelGroup } from '@angular/forms';\r\n\r\nimport { LINE_CHART_OPTIONS } from '@com/const';\r\nimport { CellDesignMetrics } from '@com/services/cell-design-metrics.service';\r\nimport { CellDesign } from '@com/services/cell-design.service';\r\nimport { Material, MaterialVersionModel } from '@com/services/material.service';\r\nimport { deepEquals } from '@com/utils/deep-equals';\r\nimport { deepCloneDto } from '@com/utils/object';\r\nimport {\r\n    validateQValuesMinimumError,\r\n    validateQValuesComparisonError,\r\n} from '@com/app/shared/directive/validators/validate-q-values.directive';\r\nimport { validateGroupSumErrorActualNumber } from '@com/app/shared/directive/validators/validate-group-sum.directive';\r\n\r\nexport const validateFieldRequiredError = 'required';\r\n\r\nconst FORM_GROUP_NAMES = {\r\n    cathodeMaterial: 'cathode_formGroup',\r\n    cathodeQValues: 'cathode_q_values_formGroup',\r\n} as const;\r\n\r\nconst CONTROL_NAMES = {\r\n    cathodeMaterial1Weight: 'cathode_material1_weight',\r\n    cathodeMaterial2Weight: 'cathode_material2_weight',\r\n} as const;\r\n\r\n@Component({\r\n    selector: 'com-cathode-editor',\r\n    templateUrl: './cathode-editor.component.html',\r\n\r\n    // this is important for the change detection to work across components\r\n    // makes this component use the same NgForm as the parent component\r\n    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],\r\n})\r\nexport class CathodeEditorComponent implements OnInit, OnChanges {\r\n    @Input()\r\n    public get materials(): Material[] | null {\r\n        return this._materials;\r\n    }\r\n\r\n    public set materials(value: Material[] | null) {\r\n        this._materials = value;\r\n        this.blendableMaterials = this.materials?.filter((material) => material.blendable) ?? null;\r\n        this.materialMap = new Map();\r\n\r\n        this._materials?.forEach((material) => {\r\n            this.materialMap.set(material.id, material.versions);\r\n        });\r\n    }\r\n\r\n    @Input()\r\n    public loading!: boolean;\r\n\r\n    @Input()\r\n    public design!: CellDesign | null;\r\n\r\n    @Input()\r\n    public set metrics(value: CellDesignMetrics | undefined) {\r\n        if (value) {\r\n            this.createDefaultOptions();\r\n        }\r\n    }\r\n\r\n    // chart.js have the chart options defined as any\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    public options: any = {\r\n        ...deepCloneDto(LINE_CHART_OPTIONS.rootOptions),\r\n        plugins: deepCloneDto(LINE_CHART_OPTIONS.plugins),\r\n        scales: deepCloneDto(LINE_CHART_OPTIONS.scales),\r\n    };\r\n\r\n    public materialMap: Map<string, MaterialVersionModel[]> = new Map([]);\r\n\r\n    public blendableMaterials: Material[] | null = null;\r\n\r\n    public highlightedColumns: number[] = [];\r\n\r\n    public get remainingBlendableMaterials(): Material[] {\r\n        const result =\r\n            this.blendableMaterials?.filter((m) => m.id !== this.design?.cathodeMaterials[0].materialId) ?? [];\r\n        if (this._remainingBlendableMaterials === null || !deepEquals(result, this._remainingBlendableMaterials)) {\r\n            this._remainingBlendableMaterials = result;\r\n        }\r\n\r\n        return this._remainingBlendableMaterials;\r\n    }\r\n\r\n    public formGroupNames = FORM_GROUP_NAMES;\r\n    public controlNames = CONTROL_NAMES;\r\n\r\n    public readonly desiredSum = 100;\r\n    public readonly fieldRequiredError = validateFieldRequiredError;\r\n    public readonly qValuesMinimumError = validateQValuesMinimumError;\r\n    public readonly qValuesComparisonError = validateQValuesComparisonError;\r\n    public readonly groupSumErrorName = validateGroupSumErrorActualNumber;\r\n\r\n    private _materials: Material[] | null = null;\r\n\r\n    private _remainingBlendableMaterials: Material[] | null = null;\r\n\r\n    public toggleDeveloperMode(input: boolean): void {\r\n        if (this.design && this.blendableMaterials) {\r\n            this.design.cathodeMaterials = input\r\n                ? [\r\n                      {\r\n                          materialId: this.design.cathodeMaterials[0].materialId,\r\n                          materialVersionId: this.design.cathodeMaterials[0].materialVersionId,\r\n                          weightPercent: 50,\r\n                      },\r\n                      {\r\n                          materialId: this.blendableMaterials[0].id,\r\n                          materialVersionId: this.blendableMaterials[0].versions\r\n                              ? this.blendableMaterials[0].versions[0].id\r\n                              : null,\r\n                          weightPercent: 50,\r\n                      },\r\n                  ]\r\n                : [\r\n                      {\r\n                          materialId: this.design.cathodeMaterials[0].materialId,\r\n                          materialVersionId: this.design.cathodeMaterials[0].materialVersionId,\r\n                          weightPercent: 100,\r\n                      },\r\n                  ];\r\n        }\r\n    }\r\n\r\n    public ngOnInit(): void {\r\ncreateDefaultOptions()\r\n    }\r\n\r\n    public ngOnChanges(changes: SimpleChanges): void {\r\n        if (changes['metrics']) {\r\n            this.updateHighlightedColumns();\r\n        }\r\n    }\r\n\r\n    public createDefaultOptions(): void {\r\n        this.options.scales.x.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);\r\n        this.options.scales.x.title.text = $localize`:@@axisTitle.capacityCathode: Capacity / mAh/g(CAM)`;\r\n\r\n        this.options.scales.y.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);\r\n        this.options.scales.y.title.text = $localize`:@@axisTitle.voltage: Voltage vs. Li / V`;\r\n\r\n        this.options.scales.y.min = 3;\r\n\r\n        if (this.metrics) {\r\n            const box = this.metrics?.balancing.chartAnnotations;\r\n            box[0].yMin = 2.2;\r\n            box[0].yMax = 4.2;\r\n            box[0].xMin = 50;\r\n            // box[0].rotation = 90;\r\n            box[0].label.rotation = 0;\r\n            box[0].label.content = 'haker bace';\r\n            console.log('@@@box', box);\r\n            this.options.plugins.annotation = {\r\n                annotations: this.metrics?.balancing.chartAnnotations,\r\n            };\r\n        } else {\r\n            console.log('nqq metrikcs bace');\r\n        }\r\n    }\r\n\r\n    private updateHighlightedColumns(): void {\r\n        const indexOfBlendColumn = this.metrics?.cathode.summary.headers.indexOf('Blend');\r\n        const indexOfModifiedValueByTarget = this.metrics?.cathode.summary.headers.indexOf('Modified value by target');\r\n        if ((this.design?.cathodeMaterials ?? []).length > 1) {\r\n            this.highlightedColumns = [];\r\n            if (indexOfBlendColumn !== -1 && indexOfBlendColumn !== undefined) {\r\n                this.highlightedColumns.push(indexOfBlendColumn);\r\n            }\r\n        } else {\r\n            this.highlightedColumns = [];\r\n            if (indexOfModifiedValueByTarget !== -1 && indexOfModifiedValueByTarget !== undefined) {\r\n                this.highlightedColumns.push(indexOfModifiedValueByTarget);\r\n            }\r\n        }\r\n    }\r\n}\r\n", "<div *ngIf=\"materials && blendableMaterials && design\" class=\"grid align-items-stretch\">\r\n    <div class=\"col-12 lg:col-6\">\r\n        <p-card>\r\n            <ng-container\r\n                [ngModelGroup]=\"formGroupNames.cathodeMaterial\"\r\n                #cathodeMaterialFormGroup=\"ngModelGroup\"\r\n                comValidateGroupSumDirective\r\n                [desiredSum]=\"desiredSum\"\r\n                [includedControlNames]=\"[controlNames.cathodeMaterial1Weight, controlNames.cathodeMaterial2Weight]\"\r\n                [skipValidation]=\"true\"\r\n            >\r\n                <div class=\"field grid\">\r\n                    <label\r\n                        for=\"cathode_developer_mode\"\r\n                        class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                        i18n=\"@@chemicals.common.developerMode.checkbox.label\"\r\n                    >\r\n                        Entwicklermodus\r\n                    </label>\r\n                    <div class=\"col-12 xl:col-9 p-fluid\">\r\n                        <p-inputSwitch\r\n                            name=\"cathode_developer_mode\"\r\n                            inputId=\"cathode_developer_mode\"\r\n                            [ngModel]=\"design.cathodeMaterials.length > 1\"\r\n                            (ngModelChange)=\"toggleDeveloperMode($event)\"\r\n                        ></p-inputSwitch>\r\n                    </div>\r\n                </div>\r\n\r\n                <div class=\"field grid\">\r\n                    <label\r\n                        for=\"cathode_material1_id\"\r\n                        class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                        i18n=\"@@chemicals.common.material.input.label\"\r\n                    >\r\n                        Material {{ 1 }}\r\n                    </label>\r\n                    <div class=\"col-12 xl:col-9 p-fluid\">\r\n                        <p-dropdown\r\n                            name=\"cathode_material1_id\"\r\n                            inputId=\"cathode_material1_id\"\r\n                            [options]=\"design.cathodeMaterials.length > 1 ? blendableMaterials : materials\"\r\n                            [(ngModel)]=\"design.cathodeMaterials[0].materialId\"\r\n                            optionLabel=\"name\"\r\n                            optionValue=\"id\"\r\n                        ></p-dropdown>\r\n                        <div\r\n                            *ngIf=\"metrics?.cathode?.materialVoltageRanges?.[0]\"\r\n                            class=\"text-sm text-gray-300 mt-1\"\r\n                            i18n=\"@@chemicals.common.voltageRange.label\"\r\n                        >\r\n                            Spannungsbereich: {{ metrics?.cathode?.materialVoltageRanges?.[0]?.[0] }}V -\r\n                            {{ metrics?.cathode?.materialVoltageRanges?.[0]?.[1] }}V\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n\r\n                <div\r\n                    class=\"field grid\"\r\n                    [class.hidden]=\"!materialMap.get(design.cathodeMaterials[0].materialId)?.length\"\r\n                >\r\n                    <label\r\n                        for=\"cathode_material1_version_id\"\r\n                        class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                        i18n=\"@@chemicals.common.material.version.input.label\"\r\n                    >\r\n                        Version Material {{ 1 }}\r\n                    </label>\r\n                    <div class=\"col-12 xl:col-9 p-fluid\">\r\n                        <p-dropdown\r\n                            name=\"cathode_material1_version_id\"\r\n                            inputId=\"cathode_material1_version_id\"\r\n                            [options]=\"materialMap.get(design.cathodeMaterials[0].materialId)!\"\r\n                            [(ngModel)]=\"design.cathodeMaterials[0].materialVersionId\"\r\n                            optionLabel=\"description\"\r\n                            optionValue=\"id\"\r\n                            [disabled]=\"materialMap.get(design.cathodeMaterials[0].materialId)?.length === 1\"\r\n                        >\r\n                            <ng-template let-selectedVersion pTemplate=\"selectedItem\">\r\n                                <div>{{ selectedVersion.description }} - {{ selectedVersion.date | date }}</div>\r\n                            </ng-template>\r\n                            <ng-template let-version pTemplate=\"item\">\r\n                                <div>{{ version.description }} - {{ version.date | date }}</div>\r\n                            </ng-template>\r\n                        </p-dropdown>\r\n                    </div>\r\n                </div>\r\n\r\n                <ng-container *ngIf=\"design.cathodeMaterials.length > 1\">\r\n                    <div class=\"field grid\">\r\n                        <label\r\n                            [for]=\"controlNames.cathodeMaterial1Weight\"\r\n                            class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                            i18n=\"@@chemicals.common.material.ratio.input.label\"\r\n                        >\r\n                            Anteil Material {{ 1 }}\r\n                        </label>\r\n                        <div class=\"col-12 xl:col-9 p-fluid\">\r\n                            <p-inputNumber\r\n                                comCustomizeInput\r\n                                [name]=\"controlNames.cathodeMaterial1Weight\"\r\n                                [inputId]=\"controlNames.cathodeMaterial1Weight\"\r\n                                [(ngModel)]=\"design.cathodeMaterials[0].weightPercent\"\r\n                                #cathodeMaterial1WeightField=\"ngModel\"\r\n                                [maxFractionDigits]=\"2\"\r\n                                mode=\"decimal\"\r\n                                suffix=\" w%\"\r\n                                [min]=\"0\"\r\n                                [max]=\"100\"\r\n                                [required]=\"true\"\r\n                                [ngClass]=\"{\r\n                                    'ng-invalid ng-dirty': cathodeMaterial1WeightField.errors?.[fieldRequiredError]\r\n                                }\"\r\n                            ></p-inputNumber>\r\n                        </div>\r\n                        <div *ngIf=\"cathodeMaterialFormGroup.invalid\" class=\"col-12 mb-2 xl:col-3 xl:mb-0\"></div>\r\n                        <div\r\n                            *ngIf=\"cathodeMaterialFormGroup.invalid && (cathodeMaterialFormGroup.errors?.[groupSumErrorName] || cathodeMaterial1WeightField.errors?.[fieldRequiredError])\"\r\n                            class=\"com-text-error col-12 xl:col-9 p-fluid mt-1\"\r\n                        >\r\n                            <span class=\"mr-2\"><i class=\"pi pi-times-circle\"></i></span>\r\n                            <span\r\n                                *ngIf=\"cathodeMaterial1WeightField.errors?.[fieldRequiredError] && cathodeMaterial1WeightField.touched\"\r\n                                i18n=\"@@swelling.fieldIsRequired\"\r\n                            >\r\n                                Feld ist erforderlich.\r\n                            </span>\r\n                            <span\r\n                                *ngIf=\"!cathodeMaterial1WeightField.errors?.[fieldRequiredError] && cathodeMaterialFormGroup.errors?.[groupSumErrorName]\"\r\n                                i18n=\"@@materials.percentages.error\"\r\n                                >Die Summe der Prozentsätze muss 100 ergeben</span\r\n                            >\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field grid\">\r\n                        <label\r\n                            for=\"cathode_material2_id\"\r\n                            class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                            i18n=\"@@chemicals.common.material.input.label\"\r\n                        >\r\n                            Material {{ 2 }}\r\n                        </label>\r\n                        <div class=\"col-12 xl:col-9 p-fluid\">\r\n                            <p-dropdown\r\n                                name=\"cathode_material2_id\"\r\n                                inputId=\"cathode_material2_id\"\r\n                                [options]=\"remainingBlendableMaterials\"\r\n                                [(ngModel)]=\"design.cathodeMaterials[1].materialId\"\r\n                                optionLabel=\"name\"\r\n                                optionValue=\"id\"\r\n                            ></p-dropdown>\r\n                            <div\r\n                                *ngIf=\"metrics?.cathode?.materialVoltageRanges?.[1]\"\r\n                                class=\"text-sm text-gray-300 mt-1\"\r\n                                i18n=\"@@chemicals.common.voltageRange.label\"\r\n                            >\r\n                                Spannungsbereich: {{ metrics?.cathode?.materialVoltageRanges?.[1]?.[0] }}V -\r\n                                {{ metrics?.cathode?.materialVoltageRanges?.[1]?.[1] }}V\r\n                            </div>\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div\r\n                        class=\"field grid\"\r\n                        [class.hidden]=\"!materialMap.get(design.cathodeMaterials[1].materialId)?.length\"\r\n                    >\r\n                        <label\r\n                            for=\"cathode_material2_version_id\"\r\n                            class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                            i18n=\"@@chemicals.common.material.version.input.label\"\r\n                        >\r\n                            Version Material {{ 2 }}\r\n                        </label>\r\n                        <div class=\"col-12 xl:col-9 p-fluid\">\r\n                            <p-dropdown\r\n                                name=\"cathode_material2_version_id\"\r\n                                inputId=\"cathode_material2_version_id\"\r\n                                [options]=\"materialMap.get(design.cathodeMaterials[1].materialId)!\"\r\n                                [(ngModel)]=\"design.cathodeMaterials[1].materialVersionId\"\r\n                                optionLabel=\"description\"\r\n                                optionValue=\"id\"\r\n                                [disabled]=\"materialMap.get(design.cathodeMaterials[1].materialId)?.length === 1\"\r\n                            >\r\n                                <ng-template let-selectedVersion pTemplate=\"selectedItem\">\r\n                                    <div>{{ selectedVersion.description }} - {{ selectedVersion.date | date }}</div>\r\n                                </ng-template>\r\n                                <ng-template let-version pTemplate=\"item\">\r\n                                    <div>{{ version.description }} - {{ version.date | date }}</div>\r\n                                </ng-template></p-dropdown\r\n                            >\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div class=\"field grid\">\r\n                        <label\r\n                            [for]=\"controlNames.cathodeMaterial2Weight\"\r\n                            class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                            i18n=\"@@chemicals.common.material.ratio.input.label\"\r\n                        >\r\n                            Anteil Material {{ 2 }}\r\n                        </label>\r\n                        <div class=\"col-12 xl:col-9 p-fluid\">\r\n                            <p-inputNumber\r\n                                comCustomizeInput\r\n                                [name]=\"controlNames.cathodeMaterial2Weight\"\r\n                                [inputId]=\"controlNames.cathodeMaterial2Weight\"\r\n                                [(ngModel)]=\"design.cathodeMaterials[1].weightPercent\"\r\n                                #cathodeMaterial2WeightField=\"ngModel\"\r\n                                [maxFractionDigits]=\"2\"\r\n                                mode=\"decimal\"\r\n                                suffix=\" w%\"\r\n                                [min]=\"0\"\r\n                                [max]=\"100\"\r\n                                [required]=\"true\"\r\n                                [ngClass]=\"{\r\n                                    'ng-invalid ng-dirty': cathodeMaterial2WeightField.errors?.[fieldRequiredError]\r\n                                }\"\r\n                            ></p-inputNumber>\r\n                        </div>\r\n                        <div *ngIf=\"cathodeMaterialFormGroup.invalid\" class=\"col-12 mb-2 xl:col-3 xl:mb-0\"></div>\r\n                        <div\r\n                            *ngIf=\"cathodeMaterialFormGroup.invalid && (cathodeMaterialFormGroup.errors?.[groupSumErrorName] || cathodeMaterial2WeightField.errors?.[fieldRequiredError])\"\r\n                            class=\"com-text-error col-12 xl:col-9 p-fluid mt-1\"\r\n                        >\r\n                            <span class=\"mr-2\"><i class=\"pi pi-times-circle\"></i></span>\r\n                            <span\r\n                                *ngIf=\"cathodeMaterial2WeightField.errors?.[fieldRequiredError] && cathodeMaterial2WeightField.touched\"\r\n                                i18n=\"@@swelling.fieldIsRequired\"\r\n                            >\r\n                                Feld ist erforderlich.\r\n                            </span>\r\n                            <span\r\n                                *ngIf=\"!cathodeMaterial2WeightField.errors?.[fieldRequiredError] && cathodeMaterialFormGroup.errors?.[groupSumErrorName]\"\r\n                                i18n=\"@@materials.percentages.error\"\r\n                                >Die Summe der Prozentsätze muss 100 ergeben</span\r\n                            >\r\n                        </div>\r\n                    </div>\r\n\r\n                    <div\r\n                        *ngIf=\"metrics && metrics.cathode && metrics.cathode.overlappingVoltageRange\"\r\n                        class=\"field grid\"\r\n                    >\r\n                        <label\r\n                            [for]=\"controlNames.cathodeMaterial2Weight\"\r\n                            class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                            i18n=\"@@chemicals.common.overlappingVoltageRange.label\"\r\n                        >\r\n                            Überlappenden Spannungsbereich überblenden\r\n                        </label>\r\n                        <div class=\"col-12 xl:col-9 p-fluid\">\r\n                            {{ metrics.cathode.overlappingVoltageRange[0] }}V -\r\n                            {{ metrics.cathode.overlappingVoltageRange[1] }}V\r\n                        </div>\r\n                    </div>\r\n                </ng-container>\r\n                <ng-container\r\n                    [ngModelGroup]=\"formGroupNames.cathodeQValues\"\r\n                    #cathodeQValuesFormGroup=\"ngModelGroup\"\r\n                    comValidateQValuesDirective\r\n                    [minimumValue]=\"150\"\r\n                    qRevControlName=\"cathode_q_aim\"\r\n                    q1stControlName=\"cathode_q_aim_1\"\r\n                >\r\n                    <div class=\"field grid\">\r\n                        <label\r\n                            for=\"cathode_q_aim\"\r\n                            class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                            i18n=\"@@chemicals.common.qAim.input.label\"\r\n                        >\r\n                            Ziel Qrev C/10\r\n                        </label>\r\n                        <div class=\"col-12 xl:col-9 p-fluid\">\r\n                            <p-inputNumber\r\n                                comCustomizeInput\r\n                                name=\"cathode_q_aim\"\r\n                                inputId=\"cathode_q_aim\"\r\n                                [(ngModel)]=\"design.cathodeQAim\"\r\n                                mode=\"decimal\"\r\n                                [minFractionDigits]=\"2\"\r\n                                suffix=\" mAh/g\"\r\n                                [min]=\"0.001\"\r\n                                [showClear]=\"true\"\r\n                                [ngClass]=\"{\r\n                                    'ng-invalid ng-dirty': cathodeQValuesFormGroup.invalid\r\n                                }\"\r\n                            ></p-inputNumber>\r\n                        </div>\r\n                    </div>\r\n                    <div class=\"field grid\">\r\n                        <label\r\n                            for=\"cathode_q_aim_1\"\r\n                            class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                            i18n=\"@@chemicals.common.qAim1.input.label\"\r\n                        >\r\n                            Q 1st Aim\r\n                        </label>\r\n                        <div class=\"col-12 xl:col-9 p-fluid\">\r\n                            <p-inputNumber\r\n                                comCustomizeInput\r\n                                name=\"cathode_q_aim_1\"\r\n                                inputId=\"cathode_q_aim_1\"\r\n                                [(ngModel)]=\"design.cathodeQAimFirstCharge\"\r\n                                mode=\"decimal\"\r\n                                [minFractionDigits]=\"2\"\r\n                                suffix=\" mAh/g\"\r\n                                [min]=\"0.001\"\r\n                                [showClear]=\"true\"\r\n                                [ngClass]=\"{\r\n                                    'ng-invalid ng-dirty': cathodeQValuesFormGroup.invalid\r\n                                }\"\r\n                            ></p-inputNumber>\r\n                        </div>\r\n                        <div *ngIf=\"cathodeQValuesFormGroup.invalid\" class=\"col-12 mb-2 xl:col-3 xl:mb-0\"></div>\r\n                        <div\r\n                            *ngIf=\"cathodeQValuesFormGroup.invalid\"\r\n                            class=\"com-text-error col-12 xl:col-9 p-fluid mt-1\"\r\n                        >\r\n                            <span class=\"mr-2\"><i class=\"pi pi-times-circle\"></i></span>\r\n                            <span\r\n                                *ngIf=\"cathodeQValuesFormGroup.errors?.[qValuesMinimumError]\"\r\n                                i18n=\"@@qValues.minimumValue.error\"\r\n                            >\r\n                                Sowohl Ziel Qrev C/10 als auch Ziel Q1st C/10 müssen mindestens {{ 150 }} mAh/g betragen\r\n                            </span>\r\n                            <span\r\n                                *ngIf=\"!cathodeQValuesFormGroup.errors?.[qValuesMinimumError] && cathodeQValuesFormGroup.errors?.[qValuesComparisonError]\"\r\n                                i18n=\"@@qValues.comparison.error\"\r\n                            >\r\n                                Ziel Q1st C/10 muss größer sein als Ziel Qrev C/10\r\n                            </span>\r\n                        </div>\r\n                    </div>\r\n                </ng-container>\r\n            </ng-container>\r\n        </p-card>\r\n\r\n        <p-card>\r\n            <div *ngIf=\"loading\" class=\"flex justify-content-center\">\r\n                <p-progressSpinner></p-progressSpinner>\r\n            </div>\r\n\r\n            <com-summary-table\r\n                *ngIf=\"metrics\"\r\n                [headers]=\"metrics.cathode.summary.headers\"\r\n                [rows]=\"metrics.cathode.summary.rows\"\r\n                [highlightedColumns]=\"highlightedColumns\"\r\n            ></com-summary-table>\r\n        </p-card>\r\n    </div>\r\n\r\n    <div class=\"col-12 lg:col-6\">\r\n        <com-material-chart [data]=\"metrics?.cathode?.chart\" [options]=\"options\" [loading]=\"loading\" />\r\n    </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}