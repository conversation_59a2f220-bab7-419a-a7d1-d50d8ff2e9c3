{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"./config.service\";\nimport * as i2 from \"@angular/common/http\";\nexport class CellDesignMetricsService {\n  constructor(_config, _http) {\n    this._config = _config;\n    this._http = _http;\n  }\n  calculateMetrics(cellDesign) {\n    return this._http.post(`${this._config.data.baseUrl}/api/cell-design-metrics/`, cellDesign);\n  }\n}\nCellDesignMetricsService.ɵfac = function CellDesignMetricsService_Factory(t) {\n  return new (t || CellDesignMetricsService)(i0.ɵɵinject(i1.ConfigService), i0.ɵɵinject(i2.HttpClient));\n};\nCellDesignMetricsService.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n  token: CellDesignMetricsService,\n  factory: CellDesignMetricsService.ɵfac,\n  providedIn: 'root'\n});", "map": {"version": 3, "mappings": ";;;AAuQA,OAAM,MAAOA,wBAAwB;EACjCC,YAA2BC,OAAsB,EAAUC,KAAiB;IAAjD,YAAO,GAAPD,OAAO;IAAyB,UAAK,GAALC,KAAK;EAAe;EAExEC,gBAAgB,CAACC,UAAsB;IAC1C,OAAO,IAAI,CAACF,KAAK,CAACG,IAAI,CAAoB,GAAG,IAAI,CAACJ,OAAO,CAACK,IAAI,CAACC,OAAO,2BAA2B,EAAEH,UAAU,CAAC;EAClH;;AALSL,wBAAwB;mBAAxBA,wBAAwB;AAAA;AAAxBA,wBAAwB;SAAxBA,wBAAwB;EAAAS,SAAxBT,wBAAwB;EAAAU,YAFrB;AAAM", "names": ["CellDesignMetricsService", "constructor", "_config", "_http", "calculateMetrics", "cellDesign", "post", "data", "baseUrl", "factory", "providedIn"], "sourceRoot": "", "sources": ["D:\\Repos\\cellforce\\dmgmt-cell-o-mat-2.0-frontend\\src\\services\\cell-design-metrics.service.ts"], "sourcesContent": ["import { HttpClient } from '@angular/common/http';\r\nimport { Injectable } from '@angular/core';\r\nimport { Observable } from 'rxjs';\r\n\r\nimport { AnnotationTypeEnum } from '@com/const';\r\nimport { CellDesign } from './cell-design.service';\r\nimport { ConfigService } from './config.service';\r\n\r\nexport interface CellDesignMetrics {\r\n    cathode: {\r\n        summary: MaterialSummaryTable;\r\n        chart: MaterialLineChartData;\r\n        materialVoltageRanges: (number[] | null)[];\r\n        overlappingVoltageRange: number[];\r\n    };\r\n    anode: {\r\n        summary: MaterialSummaryTable;\r\n        chart: MaterialLineChartData;\r\n        materialVoltageRanges: (number[] | null)[];\r\n        overlappingVoltageRange: number[];\r\n    };\r\n    balancing: BalancingMetrics;\r\n    bom: BomMetrics;\r\n    materials: MaterialsMetrics;\r\n    electrodePair: ElectrodePairMetrics;\r\n    summary: CellSummaryMetrics;\r\n    electrolyte: ElectrolyteMetrics;\r\n    cell: CellEditorMetrics;\r\n    cellSwelling: CellSwellingMetrics;\r\n}\r\n\r\nexport interface CellSummaryMetrics {\r\n    cellWeightOverall: number;\r\n    cellVolumeOverall: number;\r\n    priceCell?: number;\r\n    safeNominalVoltageC10?: number;\r\n    safeCellCapacityC10?: number;\r\n    safeCellEnergyC10?: number;\r\n    safeCellEnergyDensityVolumetricC10?: number;\r\n    safeCellEnergyDensityGravimetricC10?: number;\r\n    safeCellPriceKwhC10?: number;\r\n    safeNominalVoltageC3?: number;\r\n    safeCellCapacityC3?: number;\r\n    safeCellEnergyC3?: number;\r\n    safeCellEnergyDensityVolumetricC3?: number;\r\n    safeCellEnergyDensityGravimetricC3?: number;\r\n    safeCellPriceKwhC3?: number;\r\n    cellCount: number;\r\n    packNominalVoltage?: number;\r\n    safePackCapacity?: number;\r\n    safePackEnergy?: number;\r\n    packWeight?: number;\r\n    packPrice?: number;\r\n}\r\n\r\nexport interface BomMetrics {\r\n    table: MaterialSummaryTable;\r\n}\r\n\r\nexport interface BalancingMetrics {\r\n    chart: MaterialLineChartData;\r\n    chartAnnotations: BoxAnnotation[];\r\n    npRatioRev: number;\r\n    hysteresis: number;\r\n    uMin: number;\r\n    uMax: number;\r\n    summary: MaterialSummaryTable;\r\n    warning: string;\r\n}\r\n\r\nexport interface ElectrolyteMetrics {\r\n    poreVolumeAh: number;\r\n    poreVolume: number;\r\n    electrolyteAmount: number;\r\n    electrolyteAmountSuggestionAh: number;\r\n    electrolyteAmountSuggestion: number;\r\n    electrolyteAmountSei: number;\r\n    firstCycleEfficiency: number;\r\n    seiGrowthMlAh: number;\r\n    seiGrowthNmAh: number;\r\n    anodeActiveSurface: number;\r\n\r\n    agingTable: MaterialSummaryTable;\r\n}\r\n\r\nexport interface ElectrodePairMetrics {\r\n    cathodePorosity: number;\r\n    anodePorosity: number;\r\n    balancing: number;\r\n    anodeAreaCapacity: number;\r\n    cathodeLoading: number;\r\n    anodeLoading: number;\r\n    cathodeCoatingThickness: number;\r\n    anodeCoatingThickness: number;\r\n    cellLayerThickness: number;\r\n    cathodeThickness: number;\r\n    anodeThickness: number;\r\n    designDatasets: ElectrodePairDesignSection[];\r\n}\r\n\r\ninterface ElectrodePairDesignSection {\r\n    label: string;\r\n    backgroundColor: string;\r\n    data: number[];\r\n}\r\n\r\nexport interface BoxAnnotation {\r\n    label: { display: boolean; content: string; color?: string; rotation?: number };\r\n    id: string;\r\n    type: AnnotationTypeEnum.box;\r\n    xMin?: number;\r\n    xMax?: number;\r\n    rotation?:\r\n    drawTime: 'beforeDatasetsDraw';\r\n    backgroundColor: string;\r\n    yMin?: number;\r\n    yMax?: number;\r\n}\r\n\r\nexport interface MaterialsMetrics {\r\n    anode: ElectrodeMaterialsMetrics;\r\n    cathode: ElectrodeMaterialsMetrics;\r\n\r\n    prelithiation: {\r\n        lithiumWeightPercent: number;\r\n        lithiumDensity: number;\r\n\r\n        activeMaterialWeightPercent: number;\r\n        material1WeightPercent: number;\r\n        material2WeightPercent?: number;\r\n\r\n        binderMaterials: { materialId: string; weightPercent: number; density: number }[];\r\n        conductiveAdditiveMaterials: { materialId: string; weightPercent: number; density: number }[];\r\n    };\r\n\r\n    electrolyteDensity: number;\r\n    separatorDensity: number;\r\n    separatorPorousness: number;\r\n    aluminiumDensity: number;\r\n    copperDensity: number;\r\n}\r\n\r\nexport interface ElectrodeMaterialsMetrics {\r\n    activeMaterialDensity: number;\r\n\r\n    material1WeightPercent: number;\r\n    material1Density: number;\r\n\r\n    material2WeightPercent?: number;\r\n    material2Density?: number;\r\n\r\n    binderMaterials: { materialId: string; weightPercent: number; density: number }[];\r\n    conductiveAdditiveMaterials: { materialId: string; weightPercent: number; density: number }[];\r\n\r\n    totalDensity: number;\r\n    fullCellQrev: number;\r\n    halfCellQrev: number;\r\n}\r\n\r\nexport interface MaterialSummaryTable {\r\n    headers: string[];\r\n    rows: MaterialSummaryTableRow[];\r\n}\r\n\r\nexport interface MaterialSummaryTableRow {\r\n    cells: MaterialSummaryTableRowCell[];\r\n    isPrimary?: boolean;\r\n}\r\n\r\nexport interface MaterialSummaryTableRowCell {\r\n    unit?: string | null;\r\n    value: SummaryCellAcceptedTypes;\r\n    editable?: TableRowCellEditableApiData;\r\n}\r\n\r\nexport interface TableRowCellEditableApiData {\r\n    id: string;\r\n    min?: number;\r\n    max?: number;\r\n}\r\n\r\nexport type SummaryCellAcceptedTypes = number | string;\r\n\r\nexport interface MaterialLineChartData {\r\n    headers: string[];\r\n    datasets: MaterialLineChartDataset[];\r\n}\r\n\r\nexport interface MaterialLineChartDataset {\r\n    id: string | null;\r\n    label: string;\r\n    description: string;\r\n    hidden: boolean;\r\n    data: { x: number; y: number }[];\r\n    borderColor: string;\r\n    borderDash: number[];\r\n    borderWidth: number;\r\n}\r\n\r\nexport interface CellEditorMetrics {\r\n    cellVolume: number; // Zellvolumen [ml]\r\n    housingWeight: number; // Gehäusegewicht (inkl. Tabs) [g]\r\n    cathodeCoatingArea: number; // Beschichtungsfläche Kathode [mm²]\r\n    anodeCoatingArea: number; // Beschichtungsfläche Anode [mm²]\r\n    separatorCoatingArea: number; // Beschichtungsfläche Separator [mm²]\r\n    electrolyteSwelling: number; // Elektrolytswelling [mm]\r\n    assemblyClearance: number; // Montagefreiraum [mm]\r\n    activeLayerCount: number; // Anzahl aktive Lagen\r\n    capacityPerLayerC10: number | null; // Ladung pro Zelllage C/10 [Ah]\r\n    energyPerLayerC10: number | null; // Energie pro Zelllage C/10 [Wh]\r\n    capacityPerLayerC3: number | null; // Ladung pro Zelllage C/3 [Ah]\r\n    energyPerLayerC3: number | null; // Energie pro Zelllage C/3 [Wh]\r\n    separatorAreaTotal: number; // Gesamtfläche Separator[m²]\r\n    cellLayerDelta?: number;\r\n}\r\n\r\nexport interface CellEditorPouchMetrics extends CellEditorMetrics {\r\n    cellLayerThicknessMax: number; // Max.Gesamtdicke Zelllagen[mm]\r\n    cellLayerThicknessTotal: number; // Gesamtdicke Zelllagen[mm]\r\n    cellLayerDelta: number; // Freiraum Zelllagen[μm]\r\n    ratioCellLayerDeltaThickness: number; // Freiraum Zelllagen / Dicke Zelllage\r\n    cathodeLayerCount: number; // Anzahl Lagen Kathode\r\n    anodeLayerCount: number; // Anzahl Lagen Anode\r\n    separatorLayerCount: number; // Anzahl Lagen Separator\r\n}\r\n\r\nexport interface CellEditorPrismaMetrics extends CellEditorMetrics {\r\n    cellLayerThicknessMax: number; // Max.Gesamtdicke Zelllagen[mm]\r\n    cellLayerThicknessTotal: number; // Gesamtdicke Zelllagen[mm]\r\n    cellLayerDelta: number; // Freiraum Zelllagen[μm]\r\n    ratioCellLayerDeltaThickness: number; // Freiraum Zelllagen / Dicke Zelllage\r\n    cathodeLayerCount: number; // Anzahl Lagen Kathode\r\n    anodeLayerCount: number; // Anzahl Lagen Anode\r\n    separatorLayerCount: number; // Anzahl Lagen Separator\r\n}\r\n\r\nexport interface CellEditorCylinderMetrics extends CellEditorMetrics {\r\n    cathodeCoatingLength: number; // Beschichtungslänge Kathode[mm]\r\n    anodeCoatingLength: number; // Beschichtungslänge Anode[mm]\r\n    separatorCoatingLength: number; // Beschichtungslänge Separator[mm]\r\n    cellLayerDiameterMax: number; // Max. Gesamtdurchmesser Zelllagen[mm]\r\n    cellLayerDiameterTotal: number; // Gesamtdurchmesser Zelllagen[mm]\r\n    cathodeLengthTotal: number; // Gesamtlänge Kathode[mm]\r\n    anodeLengthTotal: number; // Gesamtlänge Anode[mm]\r\n    cathodeWindingCount: number; // Anzahl Windungen Kathode\r\n    anodeWindingCount: number; // Anzahl Windungen Anode\r\n    separatorWindingCount: number; // Anzahl Windungen Separator\r\n}\r\nexport interface CellSwellingMetrics {\r\n    cf3AbsolutBreathingWithCompression: number;\r\n    cf3BreathingWithCompression: number;\r\n    cf3UncompressedBreathing: number;\r\n    freeSpaceAfterFormation: number;\r\n    swellingBuffer: number;\r\n    stackBreathing: number;\r\n    totalBreathingPerLayer: number;\r\n    swellingConstantA: number;\r\n    swellingConstantB: number;\r\n}\r\n\r\n@Injectable({\r\n    providedIn: 'root',\r\n})\r\nexport class CellDesignMetricsService {\r\n    public constructor(private _config: ConfigService, private _http: HttpClient) {}\r\n\r\n    public calculateMetrics(cellDesign: CellDesign): Observable<CellDesignMetrics> {\r\n        return this._http.post<CellDesignMetrics>(`${this._config.data.baseUrl}/api/cell-design-metrics/`, cellDesign);\r\n    }\r\n}\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}