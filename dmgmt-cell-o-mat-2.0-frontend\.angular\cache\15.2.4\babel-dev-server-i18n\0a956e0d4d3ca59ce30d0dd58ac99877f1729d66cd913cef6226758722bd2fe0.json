{"ast": null, "code": "import { <PERSON><PERSON><PERSON><PERSON>, NgModelGroup } from '@angular/forms';\nimport { NG_MODEL_GROUP_NAMES, NG_MODEL_MAIN_SECTION_NAMES } from '@com/app/cell-design-page/const';\nimport { ToastPositionEnum, ToastSeverityEnum } from '@com/app/prime-ng';\nimport { deepCloneDto } from '@com/utils/object';\nimport { LINE_CHART_OPTIONS } from '@com/const';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"primeng/api\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/forms\";\nimport * as i4 from \"../../prime-ng/customize-prime-input.directive\";\nimport * as i5 from \"primeng/inputnumber\";\nimport * as i6 from \"primeng/card\";\nimport * as i7 from \"primeng/progressspinner\";\nimport * as i8 from \"../summary-table/summary-table.component\";\nimport * as i9 from \"../material-chart/material-chart.component\";\nimport * as i10 from \"@com/pipe/decimal.pipe\";\nfunction BalancingEditorComponent_div_0_strong_15_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"strong\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"numberTwoFractionDigits\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", i0.ɵɵpipeBind1(2, 1, ctx_r1.metrics.npRatioRev), \" \");\n  }\n}\nfunction BalancingEditorComponent_div_0_strong_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"strong\", 23);\n    i0.ɵɵi18n(1, 24);\n    i0.ɵɵpipe(2, \"number\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵi18nExp(i0.ɵɵpipeBind2(2, 1, ctx_r4.metrics.hysteresis, \"1.0-0\"));\n    i0.ɵɵi18nApply(1);\n  }\n}\nfunction BalancingEditorComponent_div_0_div_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 25);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction BalancingEditorComponent_div_0_com_summary_table_40_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"com-summary-table\", 26);\n  }\n  if (rf & 2) {\n    const ctx_r6 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"headers\", ctx_r6.metrics.summary.headers)(\"rows\", ctx_r6.metrics.summary.rows);\n  }\n}\nfunction BalancingEditorComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"p-card\")(3, \"h5\");\n    i0.ɵɵi18n(4, 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 4)(6, \"label\", 5);\n    i0.ɵɵi18n(7, 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 7)(9, \"p-inputNumber\", 8);\n    i0.ɵɵlistener(\"ngModelChange\", function BalancingEditorComponent_div_0_Template_p_inputNumber_ngModelChange_9_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r7 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r7.design.balancingNpRatioFirst = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 4)(11, \"label\", 9)(12, \"strong\");\n    i0.ɵɵi18n(13, 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 7);\n    i0.ɵɵtemplate(15, BalancingEditorComponent_div_0_strong_15_Template, 3, 3, \"strong\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(16, \"hr\");\n    i0.ɵɵelementStart(17, \"h5\");\n    i0.ɵɵi18n(18, 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"div\", 4)(20, \"label\", 5);\n    i0.ɵɵi18n(21, 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 7)(23, \"p-inputNumber\", 14, 15);\n    i0.ɵɵlistener(\"ngModelChange\", function BalancingEditorComponent_div_0_Template_p_inputNumber_ngModelChange_23_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r9 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r9.design.balancingUMin = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(25, \"div\", 4)(26, \"label\", 5);\n    i0.ɵɵi18n(27, 16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(28, \"div\", 7)(29, \"p-inputNumber\", 17, 18);\n    i0.ɵɵlistener(\"ngModelChange\", function BalancingEditorComponent_div_0_Template_p_inputNumber_ngModelChange_29_listener($event) {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.design.balancingUMax = $event);\n    });\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(31, \"div\", 4)(32, \"label\", 9)(33, \"strong\");\n    i0.ɵɵi18n(34, 19);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"div\", 7);\n    i0.ɵɵtemplate(36, BalancingEditorComponent_div_0_strong_36_Template, 3, 4, \"strong\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelement(37, \"hr\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"p-card\");\n    i0.ɵɵtemplate(39, BalancingEditorComponent_div_0_div_39_Template, 2, 0, \"div\", 20);\n    i0.ɵɵtemplate(40, BalancingEditorComponent_div_0_com_summary_table_40_Template, 1, 2, \"com-summary-table\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 2);\n    i0.ɵɵelement(42, \"com-material-chart\", 22);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const _r2 = i0.ɵɵreference(24);\n    const _r3 = i0.ɵɵreference(30);\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"for\", ctx_r0.controlNames.npRatioFirst);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"name\", ctx_r0.controlNames.npRatioFirst)(\"inputId\", ctx_r0.controlNames.npRatioFirst)(\"ngModel\", ctx_r0.design.balancingNpRatioFirst)(\"maxFractionDigits\", ctx_r0.maxDigits)(\"min\", 0.7)(\"max\", 100)(\"required\", true);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.metrics && ctx_r0.metrics.npRatioRev);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"for\", ctx_r0.controlNames.uMin);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"name\", ctx_r0.controlNames.uMin)(\"inputId\", ctx_r0.controlNames.uMin)(\"ngModel\", ctx_r0.design.balancingUMin)(\"max\", _r3.value)(\"maxFractionDigits\", ctx_r0.maxDigits)(\"required\", true);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"for\", ctx_r0.controlNames.uMax);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"name\", ctx_r0.controlNames.uMax)(\"inputId\", ctx_r0.controlNames.uMax)(\"ngModel\", ctx_r0.design.balancingUMax)(\"min\", _r2.value)(\"maxFractionDigits\", ctx_r0.maxDigits)(\"required\", true);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.metrics && ctx_r0.metrics.hysteresis);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.loading);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.metrics);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"data\", ctx_r0.metrics == null ? null : ctx_r0.metrics.chart)(\"options\", ctx_r0.options)(\"loading\", ctx_r0.loading);\n  }\n}\nconst CONTROL_NAMES = {\n  npRatioFirst: 'balancing_np_ratio_first',\n  uMin: 'balancing_u_min',\n  uMax: 'balancing_u_max'\n};\nexport class BalancingEditorComponent {\n  set metrics(value) {\n    if (value?.warning) {\n      this._messageService.clear(ToastPositionEnum.topCenter);\n      this._messageService.add({\n        key: ToastPositionEnum.topCenter,\n        severity: ToastSeverityEnum.warning,\n        summary: $localize`:@@common.warning:Achtung`,\n        detail: value.warning\n      });\n    }\n    this._metrics = value;\n    if (value) {\n      this.createDefaultOptions();\n    }\n  }\n  get metrics() {\n    return this._metrics;\n  }\n  constructor(_messageService) {\n    this._messageService = _messageService;\n    this.controlNames = CONTROL_NAMES;\n    this.maxDigits = 20;\n    // chart.js have the chart options defined as any\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    this.options = {};\n  }\n  ngOnInit() {\n    this.createDefaultOptions();\n  }\n  getDesignPatch(metrics) {\n    const patch = {\n      [NG_MODEL_MAIN_SECTION_NAMES.chemicalDesign]: {\n        [NG_MODEL_GROUP_NAMES.balancing]: {\n          // Round to PrimeNG input prescision to avoid rounding on blur and triggering valueChanged\n          [CONTROL_NAMES.uMax]: parseFloat(metrics.balancing.uMax.toFixed(this.maxDigits)),\n          [CONTROL_NAMES.uMin]: parseFloat(metrics.balancing.uMin.toFixed(this.maxDigits))\n        }\n      }\n    };\n    return patch;\n  }\n  createDefaultOptions() {\n    this.options = {\n      ...deepCloneDto(LINE_CHART_OPTIONS.rootOptions),\n      plugins: deepCloneDto(LINE_CHART_OPTIONS.plugins),\n      scales: deepCloneDto(LINE_CHART_OPTIONS.scales)\n    };\n    this.options.scales.x.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);\n    this.options.scales.x.title.text = $localize`:@@axisTitle.capacityCathode: Capacity / mAh/g(CAM)`;\n    this.options.scales.y.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);\n    this.options.scales.y.title.text = $localize`:@@axisTitle.cellPotential: Cell potential / V`;\n    if (this.metrics) {\n      const box = this.metrics?.chartAnnotations;\n      box[0].yMin = 2.2;\n      box[0].yMax = 4.2;\n      box[0].xMin = 50;\n      box[0].label.rotation = 1;\n      box[0].label.content = 'haker bace';\n      console.log('@@@box', box);\n      this.options.plugins.annotation = {\n        annotations: this.metrics?.chartAnnotations\n      };\n    }\n  }\n}\nBalancingEditorComponent.ɵfac = function BalancingEditorComponent_Factory(t) {\n  return new (t || BalancingEditorComponent)(i0.ɵɵdirectiveInject(i1.MessageService));\n};\nBalancingEditorComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: BalancingEditorComponent,\n  selectors: [[\"com-balancing-editor\"]],\n  inputs: {\n    design: \"design\",\n    loading: \"loading\",\n    metrics: \"metrics\"\n  },\n  features: [i0.ɵɵProvidersFeature([], [{\n    provide: ControlContainer,\n    useExisting: NgModelGroup\n  }])],\n  decls: 1,\n  vars: 1,\n  consts: function () {\n    let i18n_0;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_tab_balancing$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__1 = goog.getMsg(\"Balancing\");\n      i18n_0 = MSG_EXTERNAL_tab_balancing$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__1;\n    } else {\n      i18n_0 = $localize`:@@tab.balancing:Balancing`;\n    }\n    let i18n_2;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_balancing_npRatioFirst_input_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__3 = goog.getMsg(\" N/P Verh\\u00E4ltnis Formierung \");\n      i18n_2 = MSG_EXTERNAL_balancing_npRatioFirst_input_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__3;\n    } else {\n      i18n_2 = $localize`:@@balancing.npRatioFirst.input.label: N/P Verhältnis Formierung `;\n    }\n    let i18n_4;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_balancing_npRatioRev_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__5 = goog.getMsg(\"N/P Verh\\u00E4ltnis Reversibel\");\n      i18n_4 = MSG_EXTERNAL_balancing_npRatioRev_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__5;\n    } else {\n      i18n_4 = $localize`:@@balancing.npRatioRev.label:N/P Verhältnis Reversibel`;\n    }\n    let i18n_6;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_common_voltage$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__7 = goog.getMsg(\"Spannung\");\n      i18n_6 = MSG_EXTERNAL_common_voltage$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__7;\n    } else {\n      i18n_6 = $localize`:@@common.voltage:Spannung`;\n    }\n    let i18n_8;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_balancing_uMin_input_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__9 = goog.getMsg(\" Umin \");\n      i18n_8 = MSG_EXTERNAL_balancing_uMin_input_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__9;\n    } else {\n      i18n_8 = $localize`:@@balancing.uMin.input.label: Umin `;\n    }\n    let i18n_10;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_balancing_uMax_input_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__11 = goog.getMsg(\" Umax \");\n      i18n_10 = MSG_EXTERNAL_balancing_uMax_input_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__11;\n    } else {\n      i18n_10 = $localize`:@@balancing.uMax.input.label: Umax `;\n    }\n    let i18n_12;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_balancing_hysteresis_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__13 = goog.getMsg(\"Hysterese\");\n      i18n_12 = MSG_EXTERNAL_balancing_hysteresis_label$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS__13;\n    } else {\n      i18n_12 = $localize`:@@balancing.hysteresis.label:Hysterese`;\n    }\n    let i18n_14;\n    if (typeof ngI18nClosureMode !== \"undefined\" && ngI18nClosureMode) {\n      /**\r\n       * @suppress {msgDescriptions}\r\n       */\n      const MSG_EXTERNAL_common_milliVolts$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS___15 = goog.getMsg(\" {$interpolation}\\u00A0mV \", {\n        \"interpolation\": \"\\uFFFD0\\uFFFD\"\n      }, {\n        original_code: {\n          \"interpolation\": \"{{ metrics.hysteresis | number : \\\"1.0-0\\\" }}\"\n        }\n      });\n      i18n_14 = MSG_EXTERNAL_common_milliVolts$$SRC_APP_CELL_DESIGN_PAGE_BALANCING_EDITOR_BALANCING_EDITOR_COMPONENT_TS___15;\n    } else {\n      i18n_14 = $localize`:@@common.milliVolts: ${\"\\uFFFD0\\uFFFD\"}:INTERPOLATION: mV `;\n    }\n    return [[\"class\", \"grid align-items-stretch\", 4, \"ngIf\"], [1, \"grid\", \"align-items-stretch\"], [1, \"col-12\", \"lg:col-6\"], i18n_0, [1, \"field\", \"grid\"], [1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\", 3, \"for\"], i18n_2, [1, \"col-12\", \"xl:col-9\", \"p-fluid\"], [\"comCustomizeInput\", \"\", \"mode\", \"decimal\", 3, \"name\", \"inputId\", \"ngModel\", \"maxFractionDigits\", \"min\", \"max\", \"required\", \"ngModelChange\"], [1, \"col-12\", \"mb-2\", \"xl:col-3\", \"xl:mb-0\"], i18n_4, [\"class\", \"ml-1\", 4, \"ngIf\"], i18n_6, i18n_8, [\"comCustomizeInput\", \"\", \"mode\", \"decimal\", \"suffix\", \" V\", 3, \"name\", \"inputId\", \"ngModel\", \"max\", \"maxFractionDigits\", \"required\", \"ngModelChange\"], [\"uMinInput\", \"\"], i18n_10, [\"comCustomizeInput\", \"\", \"mode\", \"decimal\", \"suffix\", \" V\", 3, \"name\", \"inputId\", \"ngModel\", \"min\", \"maxFractionDigits\", \"required\", \"ngModelChange\"], [\"uMaxInput\", \"\"], i18n_12, [\"class\", \"flex justify-content-center\", 4, \"ngIf\"], [3, \"headers\", \"rows\", 4, \"ngIf\"], [3, \"data\", \"options\", \"loading\"], [1, \"ml-1\"], i18n_14, [1, \"flex\", \"justify-content-center\"], [3, \"headers\", \"rows\"]];\n  },\n  template: function BalancingEditorComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵtemplate(0, BalancingEditorComponent_div_0_Template, 43, 29, \"div\", 0);\n    }\n    if (rf & 2) {\n      i0.ɵɵproperty(\"ngIf\", ctx.design);\n    }\n  },\n  dependencies: [i2.NgIf, i3.NgControlStatus, i3.RequiredValidator, i3.NgModel, i4.CustomizePrimeInputDirective, i5.InputNumber, i6.Card, i7.ProgressSpinner, i8.SummaryTableComponent, i9.MaterialChartComponent, i2.DecimalPipe, i10.NumberTwoFractionDigitsPipe],\n  encapsulation: 2\n});", "map": {"version": 3, "mappings": "AACA,SAASA,gBAAgB,EAAEC,YAAY,QAAQ,gBAAgB;AAM/D,SAASC,oBAAoB,EAAEC,2BAA2B,QAAQ,iCAAiC;AACnG,SAASC,iBAAiB,EAAEC,iBAAiB,QAAQ,mBAAmB;AACxE,SAASC,YAAY,QAAQ,mBAAmB;AAChD,SAASC,kBAAkB,QAAQ,YAAY;;;;;;;;;;;;;;ICsB3BC,kCAA2D;IACvDA,YACJ;;IAAAA,iBAAS;;;;IADLA,eACJ;IADIA,gFACJ;;;;;IA+DAA,kCAAsF;IAAtFA,gBAAsF;;IAEtFA,iBAAS;;;;IAATA,eAAS;IAATA,sEAAS;IAATA,iBAAS;;;;;IAQjBA,+BAAyD;IACrDA,oCAAqB;IACzBA,iBAAM;;;;;IAENA,wCAIqB;;;;IAFjBA,wDAAmC;;;;;;IAjHnDA,8BAAqD;IAGzCA,eAA2B;IAASA,iBAAK;IACzCA,8BAAwB;IACpBA,eAIC;IAEDA,iBAAQ;IACRA,8BAAqC;IAK7BA;MAAAA;MAAA;MAAA,OAAaA,4DAChC;IAAA,EAD6D;IAM7CA,iBAAgB;IAIzBA,+BAAwB;IAEhBA,iBAA4C;IAAyBA,iBAAS;IAElFA,+BAAqC;IACjCA,wFAES;IACbA,iBAAM;IAGVA,sBAAM;IACNA,2BAA4B;IAA5BA,iBAA4B;IAAQA,iBAAK;IACzCA,+BAAwB;IACpBA,iBAIC;IAEDA,iBAAQ;IAIRA,+BAAqC;IAM7BA;MAAAA;MAAA;MAAA,OAAaA,oDAChC;IAAA,EADqD;IAMrCA,iBAAgB;IAIzBA,+BAAwB;IACpBA,iBAIC;IAEDA,iBAAQ;IAERA,+BAAqC;IAM7BA;MAAAA;MAAA;MAAA,OAAaA,qDAChC;IAAA,EADqD;IAMrCA,iBAAgB;IAIzBA,+BAAwB;IAEhBA,iBAA4C;IAASA,iBAAS;IAElEA,+BAAqC;IACjCA,wFAES;IACbA,iBAAM;IAGVA,sBAAM;IACVA,iBAAS;IAETA,+BAAQ;IACJA,kFAEM;IAENA,8GAIqB;IACzBA,iBAAS;IAGbA,+BAA6B;IACzBA,0CAAsF;IAC1FA,iBAAM;;;;;;IAnHUA,eAAiC;IAAjCA,sDAAiC;IAS7BA,eAAkC;IAAlCA,uDAAkC;IAiB7BA,eAAmC;IAAnCA,kEAAmC;IAU5CA,eAAyB;IAAzBA,8CAAyB;IAarBA,eAA0B;IAA1BA,+CAA0B;IAc9BA,eAAyB;IAAzBA,8CAAyB;IAWrBA,eAA0B;IAA1BA,+CAA0B;IAiBrBA,eAAmC;IAAnCA,kEAAmC;IAU9CA,eAAa;IAAbA,qCAAa;IAKdA,eAAa;IAAbA,qCAAa;IAQFA,eAAuB;IAAvBA,2EAAuB;;;AD5GnD,MAAMC,aAAa,GAAG;EAClBC,YAAY,EAAE,0BAA0B;EACxCC,IAAI,EAAE,iBAAiB;EACvBC,IAAI,EAAE;CACA;AAmBV,OAAM,MAAOC,wBAAwB;EAOjC,IACWC,OAAO,CAACC,KAAmC;IAClD,IAAIA,KAAK,EAAEC,OAAO,EAAE;MAChB,IAAI,CAACC,eAAe,CAACC,KAAK,CAACd,iBAAiB,CAACe,SAAS,CAAC;MACvD,IAAI,CAACF,eAAe,CAACG,GAAG,CAAC;QACrBC,GAAG,EAAEjB,iBAAiB,CAACe,SAAS;QAChCG,QAAQ,EAAEjB,iBAAiB,CAACW,OAAO;QACnCO,OAAO,EAAEC,SAAS,2BAA2B;QAC7CC,MAAM,EAAEV,KAAK,CAACC;OACjB,CAAC;;IAGN,IAAI,CAACU,QAAQ,GAAGX,KAAK;IAErB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACY,oBAAoB,EAAE;;EAEnC;EAEA,IAAWb,OAAO;IACd,OAAO,IAAI,CAACY,QAAQ;EACxB;EAYAE,YAA2BX,eAA+B;IAA/B,oBAAe,GAAfA,eAAe;IAV1B,iBAAY,GAAGR,aAAa;IAE5B,cAAS,GAAG,EAAE;IAE9B;IACA;IACO,YAAO,GAAQ,EAAE;EAIqC;EAEtDoB,QAAQ;IACX,IAAI,CAACF,oBAAoB,EAAE;EAC/B;EAEOG,cAAc,CAAChB,OAA0B;IAC5C,MAAMiB,KAAK,GAAG;MACV,CAAC5B,2BAA2B,CAAC6B,cAAc,GAAG;QAC1C,CAAC9B,oBAAoB,CAAC+B,SAAS,GAAG;UAC9B;UACA,CAACxB,aAAa,CAACG,IAAI,GAAGsB,UAAU,CAACpB,OAAO,CAACmB,SAAS,CAACrB,IAAI,CAACuB,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC,CAAC;UAChF,CAAC3B,aAAa,CAACE,IAAI,GAAGuB,UAAU,CAACpB,OAAO,CAACmB,SAAS,CAACtB,IAAI,CAACwB,OAAO,CAAC,IAAI,CAACC,SAAS,CAAC;;;KAG1F;IAED,OAAOL,KAAK;EAChB;EAEQJ,oBAAoB;IACxB,IAAI,CAACU,OAAO,GAAG;MACX,GAAG/B,YAAY,CAACC,kBAAkB,CAAC+B,WAAW,CAAC;MAC/CC,OAAO,EAAEjC,YAAY,CAACC,kBAAkB,CAACgC,OAAO,CAAC;MACjDC,MAAM,EAAElC,YAAY,CAACC,kBAAkB,CAACiC,MAAM;KACjD;IAED,IAAI,CAACH,OAAO,CAACG,MAAM,CAACC,CAAC,CAACC,KAAK,GAAGpC,YAAY,CAACC,kBAAkB,CAACoC,SAAS,CAAC;IACxE,IAAI,CAACN,OAAO,CAACG,MAAM,CAACC,CAAC,CAACC,KAAK,CAACE,IAAI,GAAGpB,SAAS,qDAAqD;IAEjG,IAAI,CAACa,OAAO,CAACG,MAAM,CAACK,CAAC,CAACH,KAAK,GAAGpC,YAAY,CAACC,kBAAkB,CAACoC,SAAS,CAAC;IACxE,IAAI,CAACN,OAAO,CAACG,MAAM,CAACK,CAAC,CAACH,KAAK,CAACE,IAAI,GAAGpB,SAAS,gDAAgD;IAE5F,IAAI,IAAI,CAACV,OAAO,EAAE;MACd,MAAMgC,GAAG,GAAG,IAAI,CAAChC,OAAO,EAAEiC,gBAAgB;MAC1CD,GAAG,CAAC,CAAC,CAAC,CAACE,IAAI,GAAG,GAAG;MACjBF,GAAG,CAAC,CAAC,CAAC,CAACG,IAAI,GAAG,GAAG;MACjBH,GAAG,CAAC,CAAC,CAAC,CAACI,IAAI,GAAG,EAAE;MAChBJ,GAAG,CAAC,CAAC,CAAC,CAACK,KAAK,CAACC,QAAQ,GAAG,CAAC;MACzBN,GAAG,CAAC,CAAC,CAAC,CAACK,KAAK,CAACE,OAAO,GAAG,YAAY;MACnCC,OAAO,CAACC,GAAG,CAAC,QAAQ,EAAET,GAAG,CAAC;MAC1B,IAAI,CAACT,OAAO,CAACE,OAAO,CAACiB,UAAU,GAAG;QAC9BC,WAAW,EAAE,IAAI,CAAC3C,OAAO,EAAEiC;OAC9B;;EAET;;AArFSlC,wBAAwB;mBAAxBA,wBAAwB;AAAA;AAAxBA,wBAAwB;QAAxBA,wBAAwB;EAAA6C;EAAAC;IAAAC;IAAAC;IAAA/C;EAAA;EAAAgD,qCAFlB,CAAC;IAAEC,OAAO,EAAE/D,gBAAgB;IAAEgE,WAAW,EAAE/D;EAAY,CAAE,CAAC;EAAAgE;EAAAC;EAAAC;IAAA;IAAA;;;;;;;eC9BtC3C,qCAAS;;;;;;;;;;eAM/BA,4EAED;;;;;;;;;;eAkBgDA,kEAAyB;;;;;;;;;;eAUjDA,qCAAQ;;;;;;;;;;eAM/BA,+CAED;;;;;;;;;;gBAyBCA,+CAED;;;;;;;;;;gBAoBgDA,kDAAS;;;;;;;;;;;;;;;;gBAGiCA,kCAClF,eAA2C,qBAC/C;;;;;;MAnGpBhB,2EA0HM;;;MA1HAA,iCAAY", "names": ["ControlContainer", "NgModelGroup", "NG_MODEL_GROUP_NAMES", "NG_MODEL_MAIN_SECTION_NAMES", "ToastPositionEnum", "ToastSeverityEnum", "deepCloneDto", "LINE_CHART_OPTIONS", "i0", "CONTROL_NAMES", "npRatioFirst", "uMin", "uMax", "BalancingEditorComponent", "metrics", "value", "warning", "_messageService", "clear", "topCenter", "add", "key", "severity", "summary", "$localize", "detail", "_metrics", "createDefaultOptions", "constructor", "ngOnInit", "getDesignPatch", "patch", "chemicalDesign", "balancing", "parseFloat", "toFixed", "maxDigits", "options", "rootOptions", "plugins", "scales", "x", "title", "axisTitle", "text", "y", "box", "chartAnnotations", "yMin", "yMax", "xMin", "label", "rotation", "content", "console", "log", "annotation", "annotations", "selectors", "inputs", "design", "loading", "features", "provide", "useExisting", "decls", "vars", "consts"], "sourceRoot": "", "sources": ["D:\\Repos\\cellforce\\dmgmt-cell-o-mat-2.0-frontend\\src\\app\\cell-design-page\\balancing-editor\\balancing-editor.component.ts", "D:\\Repos\\cellforce\\dmgmt-cell-o-mat-2.0-frontend\\src\\app\\cell-design-page\\balancing-editor\\balancing-editor.component.html"], "sourcesContent": ["import { Component, Input, OnInit } from '@angular/core';\r\nimport { <PERSON><PERSON>ontainer, NgModelGroup } from '@angular/forms';\r\nimport { MessageService } from 'primeng/api';\r\n\r\nimport { BalancingMetrics, CellDesignMetrics } from '@com/services/cell-design-metrics.service';\r\nimport { CellDesign } from '@com/services/cell-design.service';\r\nimport { PatchCellDesignFromMetrics } from '@com/app/cell-design-page/types';\r\nimport { NG_MODEL_GROUP_NAMES, NG_MODEL_MAIN_SECTION_NAMES } from '@com/app/cell-design-page/const';\r\nimport { ToastPositionEnum, ToastSeverityEnum } from '@com/app/prime-ng';\r\nimport { deepCloneDto } from '@com/utils/object';\r\nimport { LINE_CHART_OPTIONS } from '@com/const';\r\n\r\nconst CONTROL_NAMES = {\r\n    npRatioFirst: 'balancing_np_ratio_first',\r\n    uMin: 'balancing_u_min',\r\n    uMax: 'balancing_u_max',\r\n} as const;\r\n\r\ntype BalancingDesignPatch = {\r\n    [NG_MODEL_MAIN_SECTION_NAMES.chemicalDesign]: {\r\n        [NG_MODEL_GROUP_NAMES.balancing]: {\r\n            [CONTROL_NAMES.uMax]: number;\r\n            [CONTROL_NAMES.uMin]: number;\r\n        };\r\n    };\r\n};\r\n\r\n@Component({\r\n    selector: 'com-balancing-editor',\r\n    templateUrl: './balancing-editor.component.html',\r\n\r\n    // this is important for the change detection to work across components\r\n    // makes this component use the same NgForm as the parent component\r\n    viewProviders: [{ provide: ControlContainer, useExisting: NgModelGroup }],\r\n})\r\nexport class BalancingEditorComponent implements PatchCellDesignFromMetrics<BalancingDesignPatch>, OnInit {\r\n    @Input()\r\n    public design: CellDesign | null;\r\n\r\n    @Input()\r\n    public loading: boolean;\r\n\r\n    @Input()\r\n    public set metrics(value: BalancingMetrics | undefined) {\r\n        if (value?.warning) {\r\n            this._messageService.clear(ToastPositionEnum.topCenter);\r\n            this._messageService.add({\r\n                key: ToastPositionEnum.topCenter,\r\n                severity: ToastSeverityEnum.warning,\r\n                summary: $localize`:@@common.warning:Achtung`,\r\n                detail: value.warning,\r\n            });\r\n        }\r\n\r\n        this._metrics = value;\r\n\r\n        if (value) {\r\n            this.createDefaultOptions();\r\n        }\r\n    }\r\n\r\n    public get metrics(): BalancingMetrics | undefined {\r\n        return this._metrics;\r\n    }\r\n\r\n    public readonly controlNames = CONTROL_NAMES;\r\n\r\n    public readonly maxDigits = 20;\r\n\r\n    // chart.js have the chart options defined as any\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    public options: any = {};\r\n\r\n    private _metrics: BalancingMetrics | undefined;\r\n\r\n    public constructor(private _messageService: MessageService) {}\r\n\r\n    public ngOnInit(): void {\r\n        this.createDefaultOptions();\r\n    }\r\n\r\n    public getDesignPatch(metrics: CellDesignMetrics): BalancingDesignPatch {\r\n        const patch = {\r\n            [NG_MODEL_MAIN_SECTION_NAMES.chemicalDesign]: {\r\n                [NG_MODEL_GROUP_NAMES.balancing]: {\r\n                    // Round to PrimeNG input prescision to avoid rounding on blur and triggering valueChanged\r\n                    [CONTROL_NAMES.uMax]: parseFloat(metrics.balancing.uMax.toFixed(this.maxDigits)),\r\n                    [CONTROL_NAMES.uMin]: parseFloat(metrics.balancing.uMin.toFixed(this.maxDigits)),\r\n                },\r\n            },\r\n        };\r\n\r\n        return patch;\r\n    }\r\n\r\n    private createDefaultOptions(): void {\r\n        this.options = {\r\n            ...deepCloneDto(LINE_CHART_OPTIONS.rootOptions),\r\n            plugins: deepCloneDto(LINE_CHART_OPTIONS.plugins),\r\n            scales: deepCloneDto(LINE_CHART_OPTIONS.scales),\r\n        };\r\n\r\n        this.options.scales.x.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);\r\n        this.options.scales.x.title.text = $localize`:@@axisTitle.capacityCathode: Capacity / mAh/g(CAM)`;\r\n\r\n        this.options.scales.y.title = deepCloneDto(LINE_CHART_OPTIONS.axisTitle);\r\n        this.options.scales.y.title.text = $localize`:@@axisTitle.cellPotential: Cell potential / V`;\r\n\r\n        if (this.metrics) {\r\n            const box = this.metrics?.chartAnnotations;\r\n            box[0].yMin = 2.2;\r\n            box[0].yMax = 4.2;\r\n            box[0].xMin = 50;\r\n            box[0].label.rotation = 1;\r\n            box[0].label.content = 'haker bace';\r\n            console.log('@@@box', box);\r\n            this.options.plugins.annotation = {\r\n                annotations: this.metrics?.chartAnnotations,\r\n            };\r\n        }\r\n    }\r\n}\r\n", "<div *ngIf=\"design\" class=\"grid align-items-stretch\">\r\n    <div class=\"col-12 lg:col-6\">\r\n        <p-card>\r\n            <h5 i18n=\"@@tab.balancing\">Balancing</h5>\r\n            <div class=\"field grid\">\r\n                <label\r\n                    [for]=\"controlNames.npRatioFirst\"\r\n                    class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                    i18n=\"@@balancing.npRatioFirst.input.label\"\r\n                >\r\n                    N/P Verhältnis Formierung\r\n                </label>\r\n                <div class=\"col-12 xl:col-9 p-fluid\">\r\n                    <p-inputNumber\r\n                        comCustomizeInput\r\n                        [name]=\"controlNames.npRatioFirst\"\r\n                        [inputId]=\"controlNames.npRatioFirst\"\r\n                        [(ngModel)]=\"design.balancingNpRatioFirst\"\r\n                        mode=\"decimal\"\r\n                        [maxFractionDigits]=\"maxDigits\"\r\n                        [min]=\"0.7\"\r\n                        [max]=\"100\"\r\n                        [required]=\"true\"\r\n                    ></p-inputNumber>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid\">\r\n                <label class=\"col-12 mb-2 xl:col-3 xl:mb-0\">\r\n                    <strong i18n=\"@@balancing.npRatioRev.label\">N/P Verhältnis Reversibel</strong>\r\n                </label>\r\n                <div class=\"col-12 xl:col-9 p-fluid\">\r\n                    <strong *ngIf=\"metrics && metrics.npRatioRev\" class=\"ml-1\">\r\n                        {{ metrics.npRatioRev | numberTwoFractionDigits }}\r\n                    </strong>\r\n                </div>\r\n            </div>\r\n\r\n            <hr />\r\n            <h5 i18n=\"@@common.voltage\">Spannung</h5>\r\n            <div class=\"field grid\">\r\n                <label\r\n                    [for]=\"controlNames.uMin\"\r\n                    class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                    i18n=\"@@balancing.uMin.input.label\"\r\n                >\r\n                    Umin\r\n                </label>\r\n                <!-- TODO: Verify correct maxFractionDigits - 'self-correcting' behavior on Umin/Umax depends on more fraction digits,\r\n                but PrimeNG always rounds to the maxFraction digits, which is used for display as well as rounding and is at most 20 -->\r\n                <!-- TODO: Verify min/max -->\r\n                <div class=\"col-12 xl:col-9 p-fluid\">\r\n                    <p-inputNumber\r\n                        comCustomizeInput\r\n                        #uMinInput\r\n                        [name]=\"controlNames.uMin\"\r\n                        [inputId]=\"controlNames.uMin\"\r\n                        [(ngModel)]=\"design.balancingUMin\"\r\n                        [max]=\"uMaxInput.value\"\r\n                        mode=\"decimal\"\r\n                        suffix=\" V\"\r\n                        [maxFractionDigits]=\"maxDigits\"\r\n                        [required]=\"true\"\r\n                    ></p-inputNumber>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid\">\r\n                <label\r\n                    [for]=\"controlNames.uMax\"\r\n                    class=\"col-12 mb-2 xl:col-3 xl:mb-0\"\r\n                    i18n=\"@@balancing.uMax.input.label\"\r\n                >\r\n                    Umax\r\n                </label>\r\n                <!-- TODO: Verify correct maxFractionDigits & min/max -->\r\n                <div class=\"col-12 xl:col-9 p-fluid\">\r\n                    <p-inputNumber\r\n                        #uMaxInput\r\n                        comCustomizeInput\r\n                        [name]=\"controlNames.uMax\"\r\n                        [inputId]=\"controlNames.uMax\"\r\n                        [(ngModel)]=\"design.balancingUMax\"\r\n                        [min]=\"uMinInput.value\"\r\n                        mode=\"decimal\"\r\n                        suffix=\" V\"\r\n                        [maxFractionDigits]=\"maxDigits\"\r\n                        [required]=\"true\"\r\n                    ></p-inputNumber>\r\n                </div>\r\n            </div>\r\n\r\n            <div class=\"field grid\">\r\n                <label class=\"col-12 mb-2 xl:col-3 xl:mb-0\">\r\n                    <strong i18n=\"@@balancing.hysteresis.label\">Hysterese</strong>\r\n                </label>\r\n                <div class=\"col-12 xl:col-9 p-fluid\">\r\n                    <strong *ngIf=\"metrics && metrics.hysteresis\" class=\"ml-1\" i18n=\"@@common.milliVolts\">\r\n                        {{ metrics.hysteresis | number : \"1.0-0\" }}&nbsp;mV\r\n                    </strong>\r\n                </div>\r\n            </div>\r\n\r\n            <hr />\r\n        </p-card>\r\n\r\n        <p-card>\r\n            <div *ngIf=\"loading\" class=\"flex justify-content-center\">\r\n                <p-progressSpinner />\r\n            </div>\r\n\r\n            <com-summary-table\r\n                *ngIf=\"metrics\"\r\n                [headers]=\"metrics.summary.headers\"\r\n                [rows]=\"metrics.summary.rows\"\r\n            ></com-summary-table>\r\n        </p-card>\r\n    </div>\r\n\r\n    <div class=\"col-12 lg:col-6\">\r\n        <com-material-chart [data]=\"metrics?.chart\" [options]=\"options\" [loading]=\"loading\" />\r\n    </div>\r\n</div>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}