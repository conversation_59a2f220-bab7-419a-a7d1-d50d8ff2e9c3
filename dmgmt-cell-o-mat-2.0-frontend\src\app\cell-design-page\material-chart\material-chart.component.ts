import { Component, Input, ViewChild } from '@angular/core';
import { AnnotationTypeEnum, ChartTypeEnum } from '@com/const';
import { MaterialLineChartData, MaterialLineChartDataset } from '@com/services/cell-design-metrics.service';
import { splitByMaxLength } from '@com/utils/array';
import { UIChart } from 'primeng/chart';

type DatasetGrouping = MaterialLineChartDataset & { index: number };

@Component({
    selector: 'com-material-chart',
    templateUrl: 'material-chart.component.html',
    styleUrls: ['material-chart.component.scss'],
})
export class MaterialChartComponent {
    @ViewChild(UIChart)
    public uiChart: UIChart;

    @Input()
    public loading: boolean;

    @Input()
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public set options(value: any) {
        this._options = value;

        this.setAnotations();
    }

    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    public get options(): any {
        return this._options;
    }

    @Input()
    public set data(value: MaterialLineChartData | undefined) {
        this._lineChartData = value;
        if (this._lineChartData) {
            // TODO: No mapping?
            this._lineChartData.datasets = this._lineChartData.datasets.map((v) => ({ ...v, borderWidth: 4 }));
        }

        this.updateDatasetGroups();
    }

    public get data(): MaterialLineChartData | undefined {
        return this._lineChartData;
    }

    public lineType: ChartTypeEnum = ChartTypeEnum.line;

    public groupedDatasets: DatasetGrouping[][];

    public datasetVisibilities: boolean[];

    public annotations: { name: string; color: string }[] | undefined;

    private _lineChartData: MaterialLineChartData | undefined;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    private _options: any = {};

    public updateDatasetGroups(): void {
        if (this.data) {
            this.groupedDatasets = splitByMaxLength(this.data.datasets, this.data.headers.length, (item, index) => ({
                ...item,
                index,
            }));

            this.datasetVisibilities = this.data.datasets.map((data) => !data.hidden);
        }
    }

    public toggleDataset(index: number): void {
        this.uiChart.chart.setDatasetVisibility(index, !this.uiChart.chart.isDatasetVisible(index));
        this.uiChart.chart.update();
    }

    private setAnotations(): void {
        this.annotations = [];

        if (!this.options?.plugins?.annotation?.annotations) {
            return;
        }

        const boxAnnotationMap = new Map<string, string>();
        console.log('setvam -90');
        Object.keys(this.options.plugins.annotation.annotations).forEach((key: string) => {
            if (
                this.options.plugins.annotation.annotations[key] &&
                this.options.plugins.annotation.annotations[key].type === AnnotationTypeEnum.box
            ) {
                this.options.plugins.annotation.annotations[key].label = {
                    ...this.options.plugins.annotation.annotations[key].label,
                    // TODO: Color from CSS?
                    color: 'rgba(255, 255, 255, 0.87)',
                    rotation: -90,
                };
                boxAnnotationMap.set(
                    this.options.plugins.annotation.annotations[key].label.content,
                    this.options.plugins.annotation.annotations[key].backgroundColor
                );
            }
        });

        for (const [key, value] of boxAnnotationMap) {
            this.annotations.push({ name: key, color: value });
        }
    }
}
