from dataclasses import dataclass
from typing import Optional, Union

from models.chart_model import BoxAnnotation, ChartModel
from models.table_model import TableModel

@dataclass
class MaterialMetricsModel:
    material_id: str
    weight_percent: float
    density: float

@dataclass
class CellDesignMetricsElectrodeModel:
    summary: TableModel
    chart: ChartModel
    chart_annotations: Optional[list[BoxAnnotation]] = None
    material_voltage_ranges: Optional[list[Optional[list[float]]]] = None
    overlapping_voltage_range: Optional[list[float]] = None

@dataclass
class CellDesignMetricsBalancingModel:
    summary: TableModel
    chart: ChartModel
    chart_annotations: list[BoxAnnotation]
    np_ratio_rev: Optional[float]
    hysteresis: Optional[float]
    u_min: Optional[float]
    u_max: Optional[float]
    warning: Optional[str]

@dataclass
class CellDesignMetricsMaterialsElectrodeModel:
    active_material_density: float
    material1_weight_percent: float
    material1_density: float
    material2_weight_percent: Optional[float]
    material2_density: Optional[float]
    binder_materials: list[MaterialMetricsModel]
    conductive_additive_materials: list[MaterialMetricsModel]
    total_density: float
    full_cell_qrev: float
    half_cell_qrev: float

@dataclass
class CellDesignMetricsMaterialsPrelithiationModel:
    lithium_weight_percent: float
    lithium_density: float
    active_material_weight_percent: float
    conductive_additive_materials: list[MaterialMetricsModel]
    material1_weight_percent: float
    material2_weight_percent: Optional[float]
    binder_materials: list[MaterialMetricsModel]

@dataclass
class CellDesignMetricsMaterialsModel:
    anode: CellDesignMetricsMaterialsElectrodeModel
    cathode: CellDesignMetricsMaterialsElectrodeModel
    prelithiation: CellDesignMetricsMaterialsPrelithiationModel
    electrolyte_density: float
    separator_density: float
    separator_porousness: float
    aluminium_density: float
    copper_density: float

@dataclass
class CellDesignMetricsElectrodePairDatasetModel:
    label: str
    background_color: str
    data: list[float]

@dataclass
class CellDesignMetricsElectrodePairModel:
    cathode_porosity: float # Porosität Kathode [%]
    anode_porosity: float # Porosität Anode [%]
    balancing: float # Balancing (Anode/Kathode)
    anode_area_capacity: float # Ladungsdichte Anode [mAh/cm²]
    cathode_loading: float # Beladung Kathode [mg/cm²]
    anode_loading: float # Beladung Anode [mg/cm²]
    cathode_coating_thickness: float # Beschichtungsdicke Kathode [μm]
    anode_coating_thickness: float # Beschichtungsdicke Anode [μm]
    cell_layer_thickness: float # Dicke Zelllage [μm]
    cathode_thickness: float # Dicke Kathode [μm]
    anode_thickness: float # Dicke Anode [μm]
    design_datasets: list[CellDesignMetricsElectrodePairDatasetModel]

@dataclass
class CellDesignMetricsCellModel:
    cell_volume: float # Zellvolumen [ml]
    housing_weight: float # Gehäusegewicht (inkl. Tabs) [g]
    cathode_coating_area: float # Beschichtungsfläche Kathode [mm²]
    anode_coating_area: float # Beschichtungsfläche Anode [mm²]
    separator_coating_area: float # Beschichtungsfläche Separator [mm²]
    electrolyte_swelling: float # Elektrolytswelling [mm]
    assembly_clearance: float # Montagefreiraum [mm]
    active_layer_count: float # Anzahl aktive Lagen
    capacity_per_layer_c10: Optional[float] # Ladung pro Zelllage C/10 [Ah]
    energy_per_layer_c10: Optional[float] # Energie pro Zelllage C/10 [Wh]
    capacity_per_layer_c3: Optional[float] # Ladung pro Zelllage C/3 [Ah]
    energy_per_layer_c3: Optional[float] # Energie pro Zelllage C/3 [Wh]
    separator_area_total: float # Gesamtfläche Separator [m²]

@dataclass
class CellDesignMetricsPouchCellModel(CellDesignMetricsCellModel):
    cell_layer_thickness_max: float # Max. Gesamtdicke Zelllagen [mm]
    cell_layer_thickness_total: float # Gesamtdicke Zelllagen [mm]
    cell_layer_delta: float # Freiraum Zelllagen [μm]
    ratio_cell_layer_delta_thickness: float # Freiraum Zelllagen/Dicke Zelllage
    cathode_layer_count: float # Anzahl Lagen Kathode
    anode_layer_count: float # Anzahl Lagen Anode
    separator_layer_count: float # Anzahl Lagen Separator

@dataclass
class CellDesignMetricsPrismaCellModel(CellDesignMetricsCellModel):
    cell_layer_thickness_max: float # Max. Gesamtdicke Zelllagen [mm]
    cell_layer_thickness_total: float # Gesamtdicke Zelllagen [mm]
    cell_layer_delta: float # Freiraum Zelllagen [μm]
    ratio_cell_layer_delta_thickness: float # Freiraum Zelllagen/Dicke Zelllage
    cathode_layer_count: float # Anzahl Lagen Kathode
    anode_layer_count: float # Anzahl Lagen Anode
    separator_layer_count: float # Anzahl Lagen Separator

@dataclass
class CellDesignMetricsCylinderCellModel(CellDesignMetricsCellModel):
    cathode_coating_length: float # Beschichtungslänge Kathode [mm]
    anode_coating_length: float # Beschichtungslänge Anode [mm]
    separator_coating_length: float # Beschichtungslänge Separator [mm]
    cell_layer_diameter_max: float # Max. Gesamtdurchmesser Zelllagen [mm]
    cell_layer_diameter_total: float # Gesamtdurchmesser Zelllagen [mm]
    cathode_length_total: float # Gesamtlänge Kathode [mm]
    anode_length_total: float # Gesamtlänge Anode [mm]
    cathode_winding_count: float # Anzahl Windungen Kathode
    anode_winding_count: float # Anzahl Windungen Anode
    separator_winding_count: float # Anzahl Windungen Separator

@dataclass
class CellDesignMetricsElectrolyteModel:
    pore_volume_ah: float # Porenvolumen [ml/Ah]
    pore_volume: float # Porenvolumen [ml]
    electrolyte_amount: float # Elektrolytmenge [ml]
    electrolyte_amount_suggestion_ah: float # Vorschlag für Elektrolytmenge [ml/Ah]
    electrolyte_amount_suggestion: float # Vorschlag für Elektrolytmenge [ml]

    electrolyte_amount_sei: float # Elektrolytverbrauch durch SEI Bildung [ml/Ah] --> this currently is a constant value of 0.7
    first_cycle_efficiency: float # First Cycle Efficiency [%]
    sei_growth_ml_ah: float # SEI Wachstum pro Ladung [ml/Ah] --> this currently is a constant value of 0.61
    sei_growth_nm_ah: float # SEI Wachstum pro Ladung [nm/Ah]
    anode_active_surface: float # Aktive Oberfläche Anode [m²]

    aging_table: TableModel

@dataclass
class CellDesignMetricsSummaryModel:
    cell_weight_overall: float # Gewicht gesamte Zelle [g]
    cell_volume_overall: float # Volumen gesamte Zelle [l]
    price_cell: Optional[float] # Materialpreis pro Zelle [€]

    safe_nominal_voltage_c10: Optional[float] # Nominelle Spannung C/10 [V]
    safe_cell_capacity_c10: Optional[float] # Kapazität C/10 [Ah]
    safe_cell_energy_c10: Optional[float] # Energieinhalt C/10 [Wh]
    safe_cell_energy_density_volumetric_c10: Optional[float] # Energiedichte volumetrisch C/10 [Wh/l]
    safe_cell_energy_density_gravimetric_c10: Optional[float] # Energiedichte gravimetrisch C/10 [Wh/kg]
    safe_cell_price_kwh_c10: Optional[float] # Materialpreis pro kWh C/10 [€/kWh]
    
    safe_nominal_voltage_c3: Optional[float] # Nominelle Spannung C/3 [V]
    safe_cell_capacity_c3: Optional[float] # Kapazität C/3 [Ah]
    safe_cell_energy_c3: Optional[float] # Energieinhalt C/3 [Wh]
    safe_cell_energy_density_volumetric_c3: Optional[float] # Energiedichte volumetrisch C/3 [Wh/l]
    safe_cell_energy_density_gravimetric_c3: Optional[float] # Energiedichte gravimetrisch C/3 [Wh/kg]
    safe_cell_price_kwh_c3: Optional[float] # Materialpreis pro kWh C/3 [€/kWh]

    cell_count: int # Zellen gesamt
    pack_nominal_voltage: Optional[float] # Nominelle Spannung [V]
    safe_pack_capacity: Optional[float] # Kapazität [Ah]
    safe_pack_energy: Optional[float] # Energiegehalt [kWh]
    pack_weight: Optional[float] # Packgewicht [kg]
    pack_price: Optional[float] # Preis [€]

@dataclass
class CellDesignMetricsBillOfMaterialModel:
    table: TableModel

@dataclass
class CellDesignMetricsSwellingModel:
    swelling_constant_a:Optional[float]
    swelling_constant_b:Optional[float]
    total_breathing_per_layer: Optional[float]
    stack_breathing: Optional[float]
    cf3_uncompressed_breathing: Optional[float]
    cf3_breathing_with_compression: Optional[float]
    cf3_absolut_breathing_with_compression: Optional[float]
    free_space_after_formation: Optional[float]

@dataclass
class CellDesignMetricsModel:
    cathode: CellDesignMetricsElectrodeModel
    anode: CellDesignMetricsElectrodeModel
    balancing: CellDesignMetricsBalancingModel
    materials: CellDesignMetricsMaterialsModel
    electrode_pair: CellDesignMetricsElectrodePairModel
    cell: Union[CellDesignMetricsPouchCellModel, CellDesignMetricsPrismaCellModel, CellDesignMetricsCylinderCellModel]
    electrolyte: CellDesignMetricsElectrolyteModel
    summary: CellDesignMetricsSummaryModel
    bom: CellDesignMetricsBillOfMaterialModel
    cell_swelling: CellDesignMetricsSwellingModel
