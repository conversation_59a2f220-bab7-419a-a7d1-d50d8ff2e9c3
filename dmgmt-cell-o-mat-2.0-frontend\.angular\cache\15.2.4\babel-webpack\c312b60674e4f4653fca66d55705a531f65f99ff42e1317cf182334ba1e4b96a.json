{"ast": null, "code": "import { AnnotationTypeEnum, ChartTypeEnum } from '@com/const';\nimport { splitByMaxLength } from '@com/utils/array';\nimport { UIChart } from 'primeng/chart';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"primeng/checkbox\";\nimport * as i4 from \"primeng/api\";\nimport * as i5 from \"primeng/chart\";\nimport * as i6 from \"primeng/card\";\nimport * as i7 from \"primeng/table\";\nimport * as i8 from \"primeng/progressspinner\";\nfunction MaterialChartComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2);\n    i0.ɵɵelement(1, \"p-progressSpinner\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction MaterialChartComponent_ng_template_2_p_chart_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"p-chart\", 6);\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"type\", ctx_r3.lineType)(\"data\", ctx_r3.data)(\"options\", ctx_r3.options);\n  }\n}\nfunction MaterialChartComponent_ng_template_2_p_table_1_ng_template_1_th_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const header_r10 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", header_r10, \" \");\n  }\n}\nfunction MaterialChartComponent_ng_template_2_p_table_1_ng_template_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\");\n    i0.ɵɵelement(1, \"th\");\n    i0.ɵɵtemplate(2, MaterialChartComponent_ng_template_2_p_table_1_ng_template_1_th_2_Template, 2, 1, \"th\", 10);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const columns_r8 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", columns_r8);\n  }\n}\nconst _c0 = function () {\n  return {\n    standalone: true\n  };\n};\nfunction MaterialChartComponent_ng_template_2_p_table_1_ng_template_2_td_5_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r16 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 14)(1, \"p-checkbox\", 15);\n    i0.ɵɵlistener(\"ngModelChange\", function MaterialChartComponent_ng_template_2_p_table_1_ng_template_2_td_5_Template_p_checkbox_ngModelChange_1_listener($event) {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const row_r14 = restoredCtx.$implicit;\n      const ctx_r15 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r15.datasetVisibilities[row_r14.index] = $event);\n    })(\"ngModelChange\", function MaterialChartComponent_ng_template_2_p_table_1_ng_template_2_td_5_Template_p_checkbox_ngModelChange_1_listener() {\n      const restoredCtx = i0.ɵɵrestoreView(_r16);\n      const row_r14 = restoredCtx.$implicit;\n      const ctx_r17 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r17.toggleDataset(row_r14.index));\n    });\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const row_r14 = ctx.$implicit;\n    const ctx_r13 = i0.ɵɵnextContext(4);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngModel\", ctx_r13.datasetVisibilities[row_r14.index])(\"ngModelOptions\", i0.ɵɵpureFunction0(4, _c0))(\"disabled\", row_r14.data == null)(\"binary\", true);\n  }\n}\nconst _c1 = function (a0) {\n  return {\n    \"--line-color\": a0\n  };\n};\nfunction MaterialChartComponent_ng_template_2_p_table_1_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"tr\")(1, \"td\")(2, \"div\", 11);\n    i0.ɵɵelement(3, \"span\", 12);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(5, MaterialChartComponent_ng_template_2_p_table_1_ng_template_2_td_5_Template, 2, 5, \"td\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const rowData_r11 = ctx.$implicit;\n    let tmp_0_0;\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(3, _c1, (tmp_0_0 = rowData_r11[0].borderColor) !== null && tmp_0_0 !== undefined ? tmp_0_0 : \"var(--primary-color)\"));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", rowData_r11[0].description, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", rowData_r11);\n  }\n}\nconst _c2 = function () {\n  return {\n    \"min-width\": \"50rem\"\n  };\n};\nfunction MaterialChartComponent_ng_template_2_p_table_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p-table\", 7);\n    i0.ɵɵtemplate(1, MaterialChartComponent_ng_template_2_p_table_1_ng_template_1_Template, 3, 1, \"ng-template\", 8);\n    i0.ɵɵtemplate(2, MaterialChartComponent_ng_template_2_p_table_1_ng_template_2_Template, 6, 5, \"ng-template\", 9);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"columns\", ctx_r4.data.headers)(\"value\", ctx_r4.groupedDatasets)(\"tableStyle\", i0.ɵɵpureFunction0(3, _c2));\n  }\n}\nfunction MaterialChartComponent_ng_template_2_div_2_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 18);\n    i0.ɵɵelement(1, \"span\", 19);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const annotation_r19 = ctx.$implicit;\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngStyle\", i0.ɵɵpureFunction1(2, _c1, annotation_r19.color));\n    i0.ɵɵadvance(1);\n    i0.ɵɵtextInterpolate1(\" \", annotation_r19.name, \" \");\n  }\n}\nfunction MaterialChartComponent_ng_template_2_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 16);\n    i0.ɵɵtemplate(1, MaterialChartComponent_ng_template_2_div_2_div_1_Template, 3, 4, \"div\", 17);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r5 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r5.annotations);\n  }\n}\nfunction MaterialChartComponent_ng_template_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵtemplate(0, MaterialChartComponent_ng_template_2_p_chart_0_Template, 1, 3, \"p-chart\", 3);\n    i0.ɵɵtemplate(1, MaterialChartComponent_ng_template_2_p_table_1_Template, 3, 4, \"p-table\", 4);\n    i0.ɵɵtemplate(2, MaterialChartComponent_ng_template_2_div_2_Template, 2, 1, \"div\", 5);\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.data);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.data);\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.annotations && ctx_r2.annotations.length > 0);\n  }\n}\nexport class MaterialChartComponent {\n  constructor() {\n    this.lineType = ChartTypeEnum.line;\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    this._options = {};\n  }\n  set options(value) {\n    this._options = value;\n    this.setAnotations();\n  }\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  get options() {\n    return this._options;\n  }\n  set data(value) {\n    this._lineChartData = value;\n    if (this._lineChartData) {\n      // TODO: No mapping?\n      this._lineChartData.datasets = this._lineChartData.datasets.map(v => ({\n        ...v,\n        borderWidth: 4\n      }));\n    }\n    this.updateDatasetGroups();\n  }\n  get data() {\n    return this._lineChartData;\n  }\n  updateDatasetGroups() {\n    if (this.data) {\n      this.groupedDatasets = splitByMaxLength(this.data.datasets, this.data.headers.length, (item, index) => ({\n        ...item,\n        index\n      }));\n      this.datasetVisibilities = this.data.datasets.map(data => !data.hidden);\n    }\n  }\n  toggleDataset(index) {\n    this.uiChart.chart.setDatasetVisibility(index, !this.uiChart.chart.isDatasetVisible(index));\n    this.uiChart.chart.update();\n  }\n  setAnotations() {\n    this.annotations = [];\n    if (!this.options?.plugins?.annotation?.annotations) {\n      return;\n    }\n    const boxAnnotationMap = new Map();\n    console.log('setvam -90');\n    Object.keys(this.options.plugins.annotation.annotations).forEach(key => {\n      if (this.options.plugins.annotation.annotations[key] && this.options.plugins.annotation.annotations[key].type === AnnotationTypeEnum.box) {\n        this.options.plugins.annotation.annotations[key].label = {\n          ...this.options.plugins.annotation.annotations[key].label,\n          // TODO: Color from CSS?\n          color: 'rgba(255, 255, 255, 0.87)',\n          rotation: -90\n        };\n        boxAnnotationMap.set(this.options.plugins.annotation.annotations[key].label.content, this.options.plugins.annotation.annotations[key].backgroundColor);\n      }\n    });\n    for (const [key, value] of boxAnnotationMap) {\n      this.annotations.push({\n        name: key,\n        color: value\n      });\n    }\n  }\n}\nMaterialChartComponent.ɵfac = function MaterialChartComponent_Factory(t) {\n  return new (t || MaterialChartComponent)();\n};\nMaterialChartComponent.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n  type: MaterialChartComponent,\n  selectors: [[\"com-material-chart\"]],\n  viewQuery: function MaterialChartComponent_Query(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵviewQuery(UIChart, 5);\n    }\n    if (rf & 2) {\n      let _t;\n      i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.uiChart = _t.first);\n    }\n  },\n  inputs: {\n    loading: \"loading\",\n    options: \"options\",\n    data: \"data\"\n  },\n  decls: 4,\n  vars: 2,\n  consts: [[\"class\", \"flex justify-content-center\", 4, \"ngIf\", \"ngIfElse\"], [\"widgets\", \"\"], [1, \"flex\", \"justify-content-center\"], [\"height\", \"40rem\", 3, \"type\", \"data\", \"options\", 4, \"ngIf\"], [3, \"columns\", \"value\", \"tableStyle\", 4, \"ngIf\"], [\"class\", \"flex flex-wrap mt-3\", 4, \"ngIf\"], [\"height\", \"40rem\", 3, \"type\", \"data\", \"options\"], [3, \"columns\", \"value\", \"tableStyle\"], [\"pTemplate\", \"header\"], [\"pTemplate\", \"body\"], [4, \"ngFor\", \"ngForOf\"], [1, \"flex\"], [1, \"legend-line-indicator\", \"m-2\", 3, \"ngStyle\"], [\"class\", \"content-center\", 4, \"ngFor\", \"ngForOf\"], [1, \"content-center\"], [3, \"ngModel\", \"ngModelOptions\", \"disabled\", \"binary\", \"ngModelChange\"], [1, \"flex\", \"flex-wrap\", \"mt-3\"], [\"class\", \"flex m-auto\", 4, \"ngFor\", \"ngForOf\"], [1, \"flex\", \"m-auto\"], [1, \"legend-line-indicator\", \"m-1\", 3, \"ngStyle\"]],\n  template: function MaterialChartComponent_Template(rf, ctx) {\n    if (rf & 1) {\n      i0.ɵɵelementStart(0, \"p-card\");\n      i0.ɵɵtemplate(1, MaterialChartComponent_div_1_Template, 2, 0, \"div\", 0);\n      i0.ɵɵtemplate(2, MaterialChartComponent_ng_template_2_Template, 3, 3, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n      i0.ɵɵelementEnd();\n    }\n    if (rf & 2) {\n      const _r1 = i0.ɵɵreference(3);\n      i0.ɵɵadvance(1);\n      i0.ɵɵproperty(\"ngIf\", ctx.loading)(\"ngIfElse\", _r1);\n    }\n  },\n  dependencies: [i1.NgForOf, i1.NgIf, i1.NgStyle, i2.NgControlStatus, i2.NgModel, i3.Checkbox, i4.PrimeTemplate, i5.UIChart, i6.Card, i7.Table, i8.ProgressSpinner],\n  styles: [\".legend-line-indicator[_ngcontent-%COMP%] {\\n  display: inline-block;\\n  width: 3rem;\\n  background-color: var(--line-color);\\n}\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvY2VsbC1kZXNpZ24tcGFnZS9tYXRlcmlhbC1jaGFydC9tYXRlcmlhbC1jaGFydC5jb21wb25lbnQuc2NzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNJLHFCQUFBO0VBQ0EsV0FBQTtFQUNBLG1DQUFBO0FBQ0oiLCJzb3VyY2VzQ29udGVudCI6WyIubGVnZW5kLWxpbmUtaW5kaWNhdG9yIHtcclxuICAgIGRpc3BsYXk6IGlubGluZS1ibG9jaztcclxuICAgIHdpZHRoOiAzcmVtO1xyXG4gICAgYmFja2dyb3VuZC1jb2xvcjogdmFyKC0tbGluZS1jb2xvcik7XHJcbn1cclxuIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n});", "map": {"version": 3, "mappings": "AACA,SAASA,kBAAkB,EAAEC,aAAa,QAAQ,YAAY;AAE9D,SAASC,gBAAgB,QAAQ,kBAAkB;AACnD,SAASC,OAAO,QAAQ,eAAe;;;;;;;;;;;;ICHnCC,8BAAuE;IACnEA,oCAAqB;IACzBA,iBAAM;;;;;IAGFA,6BAA2F;;;;IAAtDA,sCAAiB;;;;;IAU1CA,0BAAmC;IAC/BA,YACJ;IAAAA,iBAAK;;;;IADDA,eACJ;IADIA,2CACJ;;;;;IAJJA,0BAAI;IACAA,qBAAS;IACTA,4GAEK;IACTA,iBAAK;;;;IAHsBA,eAAU;IAAVA,oCAAU;;;;;;;;;;;IAgBjCA,8BAAuD;IAE/CA;MAAA;MAAA;MAAA;MAAA;IAAA,EAA4C;MAAA;MAAA;MAAA;MAAA,OAE3BA,mDAAwB;IAAA,EAFG;IAK/CA,iBAAa;;;;;IALVA,eAA4C;IAA5CA,oEAA4C;;;;;;;;;;IAZxDA,0BAAI;IAGQA,2BAGQ;IACRA,YACJ;IAAAA,iBAAM;IAEVA,4GAQK;IACTA,iBAAK;;;;;IAdWA,eAAgF;IAAhFA,iKAAgF;IAEpFA,eACJ;IADIA,2DACJ;IAEgBA,eAAU;IAAVA,qCAAU;;;;;;;;;;IAzB1CA,kCAKC;IACGA,+GAOc;IACdA,+GAqBc;IAClBA,iBAAU;;;;IAlCNA,6CAAwB;;;;;IAqCxBA,+BAAgE;IAC5DA,2BAAgG;IAChGA,YACJ;IAAAA,iBAAM;;;;IAFsCA,eAAgD;IAAhDA,0EAAgD;IACxFA,eACJ;IADIA,oDACJ;;;;;IAJJA,+BAA+E;IAC3EA,4FAGM;IACVA,iBAAM;;;;IAJ0BA,eAAc;IAAdA,4CAAc;;;;;IAxC9CA,6FAA2F;IAC3FA,6FAoCU;IAEVA,qFAKM;;;;IA5CIA,kCAAU;IAEfA,eAAU;IAAVA,kCAAU;IAqCTA,eAA2C;IAA3CA,0EAA2C;;;ADhCzD,OAAM,MAAOC,sBAAsB;EALnCC;IA2CW,aAAQ,GAAkBL,aAAa,CAACM,IAAI;IASnD;IACQ,aAAQ,GAAQ,EAAE;;EAzC1B,IAKWC,OAAO,CAACC,KAAU;IACzB,IAAI,CAACC,QAAQ,GAAGD,KAAK;IAErB,IAAI,CAACE,aAAa,EAAE;EACxB;EAEA;EACA,IAAWH,OAAO;IACd,OAAO,IAAI,CAACE,QAAQ;EACxB;EAEA,IACWE,IAAI,CAACH,KAAwC;IACpD,IAAI,CAACI,cAAc,GAAGJ,KAAK;IAC3B,IAAI,IAAI,CAACI,cAAc,EAAE;MACrB;MACA,IAAI,CAACA,cAAc,CAACC,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACC,QAAQ,CAACC,GAAG,CAAEC,CAAC,KAAM;QAAE,GAAGA,CAAC;QAAEC,WAAW,EAAE;MAAC,CAAE,CAAC,CAAC;;IAGtG,IAAI,CAACC,mBAAmB,EAAE;EAC9B;EAEA,IAAWN,IAAI;IACX,OAAO,IAAI,CAACC,cAAc;EAC9B;EAcOK,mBAAmB;IACtB,IAAI,IAAI,CAACN,IAAI,EAAE;MACX,IAAI,CAACO,eAAe,GAAGjB,gBAAgB,CAAC,IAAI,CAACU,IAAI,CAACE,QAAQ,EAAE,IAAI,CAACF,IAAI,CAACQ,OAAO,CAACC,MAAM,EAAE,CAACC,IAAI,EAAEC,KAAK,MAAM;QACpG,GAAGD,IAAI;QACPC;OACH,CAAC,CAAC;MAEH,IAAI,CAACC,mBAAmB,GAAG,IAAI,CAACZ,IAAI,CAACE,QAAQ,CAACC,GAAG,CAAEH,IAAI,IAAK,CAACA,IAAI,CAACa,MAAM,CAAC;;EAEjF;EAEOC,aAAa,CAACH,KAAa;IAC9B,IAAI,CAACI,OAAO,CAACC,KAAK,CAACC,oBAAoB,CAACN,KAAK,EAAE,CAAC,IAAI,CAACI,OAAO,CAACC,KAAK,CAACE,gBAAgB,CAACP,KAAK,CAAC,CAAC;IAC3F,IAAI,CAACI,OAAO,CAACC,KAAK,CAACG,MAAM,EAAE;EAC/B;EAEQpB,aAAa;IACjB,IAAI,CAACqB,WAAW,GAAG,EAAE;IAErB,IAAI,CAAC,IAAI,CAACxB,OAAO,EAAEyB,OAAO,EAAEC,UAAU,EAAEF,WAAW,EAAE;MACjD;;IAGJ,MAAMG,gBAAgB,GAAG,IAAIC,GAAG,EAAkB;IAClDC,OAAO,CAACC,GAAG,CAAC,YAAY,CAAC;IACzBC,MAAM,CAACC,IAAI,CAAC,IAAI,CAAChC,OAAO,CAACyB,OAAO,CAACC,UAAU,CAACF,WAAW,CAAC,CAACS,OAAO,CAAEC,GAAW,IAAI;MAC7E,IACI,IAAI,CAAClC,OAAO,CAACyB,OAAO,CAACC,UAAU,CAACF,WAAW,CAACU,GAAG,CAAC,IAChD,IAAI,CAAClC,OAAO,CAACyB,OAAO,CAACC,UAAU,CAACF,WAAW,CAACU,GAAG,CAAC,CAACC,IAAI,KAAK3C,kBAAkB,CAAC4C,GAAG,EAClF;QACE,IAAI,CAACpC,OAAO,CAACyB,OAAO,CAACC,UAAU,CAACF,WAAW,CAACU,GAAG,CAAC,CAACG,KAAK,GAAG;UACrD,GAAG,IAAI,CAACrC,OAAO,CAACyB,OAAO,CAACC,UAAU,CAACF,WAAW,CAACU,GAAG,CAAC,CAACG,KAAK;UACzD;UACAC,KAAK,EAAE,2BAA2B;UAClCC,QAAQ,EAAE,CAAC;SACd;QACDZ,gBAAgB,CAACa,GAAG,CAChB,IAAI,CAACxC,OAAO,CAACyB,OAAO,CAACC,UAAU,CAACF,WAAW,CAACU,GAAG,CAAC,CAACG,KAAK,CAACI,OAAO,EAC9D,IAAI,CAACzC,OAAO,CAACyB,OAAO,CAACC,UAAU,CAACF,WAAW,CAACU,GAAG,CAAC,CAACQ,eAAe,CACnE;;IAET,CAAC,CAAC;IAEF,KAAK,MAAM,CAACR,GAAG,EAAEjC,KAAK,CAAC,IAAI0B,gBAAgB,EAAE;MACzC,IAAI,CAACH,WAAW,CAACmB,IAAI,CAAC;QAAEC,IAAI,EAAEV,GAAG;QAAEI,KAAK,EAAErC;MAAK,CAAE,CAAC;;EAE1D;;AAhGSJ,sBAAsB;mBAAtBA,sBAAsB;AAAA;AAAtBA,sBAAsB;QAAtBA,sBAAsB;EAAAgD;EAAAC;IAAA;qBACpBnD,OAAO;;;;;;;;;;;;;;;;;MCdtBC,8BAAQ;MACJA,uEAEM;MAENA,wHA8Cc;MAClBA,iBAAS;;;;MAnDCA,eAAe;MAAfA,kCAAe", "names": ["AnnotationTypeEnum", "ChartTypeEnum", "splitByMaxLength", "UIChart", "i0", "MaterialChartComponent", "constructor", "line", "options", "value", "_options", "setAnotations", "data", "_lineChartData", "datasets", "map", "v", "borderWidth", "updateDatasetGroups", "groupedDatasets", "headers", "length", "item", "index", "datasetVisibilities", "hidden", "toggleDataset", "uiChart", "chart", "setDatasetVisibility", "isDatasetVisible", "update", "annotations", "plugins", "annotation", "boxAnnotationMap", "Map", "console", "log", "Object", "keys", "for<PERSON>ach", "key", "type", "box", "label", "color", "rotation", "set", "content", "backgroundColor", "push", "name", "selectors", "viewQuery"], "sourceRoot": "", "sources": ["D:\\Repos\\cellforce\\dmgmt-cell-o-mat-2.0-frontend\\src\\app\\cell-design-page\\material-chart\\material-chart.component.ts", "D:\\Repos\\cellforce\\dmgmt-cell-o-mat-2.0-frontend\\src\\app\\cell-design-page\\material-chart\\material-chart.component.html"], "sourcesContent": ["import { Component, Input, ViewChild } from '@angular/core';\r\nimport { AnnotationTypeEnum, ChartTypeEnum } from '@com/const';\r\nimport { MaterialLineChartData, MaterialLineChartDataset } from '@com/services/cell-design-metrics.service';\r\nimport { splitByMaxLength } from '@com/utils/array';\r\nimport { UIChart } from 'primeng/chart';\r\n\r\ntype DatasetGrouping = MaterialLineChartDataset & { index: number };\r\n\r\n@Component({\r\n    selector: 'com-material-chart',\r\n    templateUrl: 'material-chart.component.html',\r\n    styleUrls: ['material-chart.component.scss'],\r\n})\r\nexport class MaterialChartComponent {\r\n    @ViewChild(UIChart)\r\n    public uiChart: UIChart;\r\n\r\n    @Input()\r\n    public loading: boolean;\r\n\r\n    @Input()\r\n    \r\n\r\n    @Input()\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    public set options(value: any) {\r\n        this._options = value;\r\n\r\n        this.setAnotations();\r\n    }\r\n\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    public get options(): any {\r\n        return this._options;\r\n    }\r\n\r\n    @Input()\r\n    public set data(value: MaterialLineChartData | undefined) {\r\n        this._lineChartData = value;\r\n        if (this._lineChartData) {\r\n            // TODO: No mapping?\r\n            this._lineChartData.datasets = this._lineChartData.datasets.map((v) => ({ ...v, borderWidth: 4 }));\r\n        }\r\n\r\n        this.updateDatasetGroups();\r\n    }\r\n\r\n    public get data(): MaterialLineChartData | undefined {\r\n        return this._lineChartData;\r\n    }\r\n\r\n    public lineType: ChartTypeEnum = ChartTypeEnum.line;\r\n\r\n    public groupedDatasets: DatasetGrouping[][];\r\n\r\n    public datasetVisibilities: boolean[];\r\n\r\n    public annotations: { name: string; color: string }[] | undefined;\r\n\r\n    private _lineChartData: MaterialLineChartData | undefined;\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    private _options: any = {};\r\n\r\n    public updateDatasetGroups(): void {\r\n        if (this.data) {\r\n            this.groupedDatasets = splitByMaxLength(this.data.datasets, this.data.headers.length, (item, index) => ({\r\n                ...item,\r\n                index,\r\n            }));\r\n\r\n            this.datasetVisibilities = this.data.datasets.map((data) => !data.hidden);\r\n        }\r\n    }\r\n\r\n    public toggleDataset(index: number): void {\r\n        this.uiChart.chart.setDatasetVisibility(index, !this.uiChart.chart.isDatasetVisible(index));\r\n        this.uiChart.chart.update();\r\n    }\r\n\r\n    private setAnotations(): void {\r\n        this.annotations = [];\r\n\r\n        if (!this.options?.plugins?.annotation?.annotations) {\r\n            return;\r\n        }\r\n\r\n        const boxAnnotationMap = new Map<string, string>();\r\n        console.log('setvam -90');\r\n        Object.keys(this.options.plugins.annotation.annotations).forEach((key: string) => {\r\n            if (\r\n                this.options.plugins.annotation.annotations[key] &&\r\n                this.options.plugins.annotation.annotations[key].type === AnnotationTypeEnum.box\r\n            ) {\r\n                this.options.plugins.annotation.annotations[key].label = {\r\n                    ...this.options.plugins.annotation.annotations[key].label,\r\n                    // TODO: Color from CSS?\r\n                    color: 'rgba(255, 255, 255, 0.87)',\r\n                    rotation: -90,\r\n                };\r\n                boxAnnotationMap.set(\r\n                    this.options.plugins.annotation.annotations[key].label.content,\r\n                    this.options.plugins.annotation.annotations[key].backgroundColor\r\n                );\r\n            }\r\n        });\r\n\r\n        for (const [key, value] of boxAnnotationMap) {\r\n            this.annotations.push({ name: key, color: value });\r\n        }\r\n    }\r\n}\r\n", "<p-card>\r\n    <div *ngIf=\"loading; else widgets\" class=\"flex justify-content-center\">\r\n        <p-progressSpinner />\r\n    </div>\r\n\r\n    <ng-template #widgets>\r\n        <p-chart *ngIf=\"data\" height=\"40rem\" [type]=\"lineType\" [data]=\"data\" [options]=\"options\" />\r\n        <p-table\r\n            *ngIf=\"data\"\r\n            [columns]=\"data.headers\"\r\n            [value]=\"groupedDatasets\"\r\n            [tableStyle]=\"{ 'min-width': '50rem' }\"\r\n        >\r\n            <ng-template pTemplate=\"header\" let-columns>\r\n                <tr>\r\n                    <th></th>\r\n                    <th *ngFor=\"let header of columns\">\r\n                        {{ header }}\r\n                    </th>\r\n                </tr>\r\n            </ng-template>\r\n            <ng-template pTemplate=\"body\" let-rowData let-columns=\"columns\">\r\n                <tr>\r\n                    <td>\r\n                        <div class=\"flex\">\r\n                            <span\r\n                                class=\"legend-line-indicator m-2\"\r\n                                [ngStyle]=\"{ '--line-color': rowData[0].borderColor ?? 'var(--primary-color)' }\"\r\n                            ></span>\r\n                            {{ rowData[0].description }}\r\n                        </div>\r\n                    </td>\r\n                    <td *ngFor=\"let row of rowData\" class=\"content-center\">\r\n                        <p-checkbox\r\n                            [(ngModel)]=\"datasetVisibilities[row.index]\"\r\n                            [ngModelOptions]=\"{ standalone: true }\"\r\n                            (ngModelChange)=\"toggleDataset(row.index)\"\r\n                            [disabled]=\"row.data == null\"\r\n                            [binary]=\"true\"\r\n                        ></p-checkbox>\r\n                    </td>\r\n                </tr>\r\n            </ng-template>\r\n        </p-table>\r\n\r\n        <div *ngIf=\"annotations && annotations.length > 0\" class=\"flex flex-wrap mt-3\">\r\n            <div *ngFor=\"let annotation of annotations\" class=\"flex m-auto\">\r\n                <span class=\"legend-line-indicator m-1\" [ngStyle]=\"{ '--line-color': annotation.color }\"></span>\r\n                {{ annotation.name }}\r\n            </div>\r\n        </div>\r\n    </ng-template>\r\n</p-card>\r\n"]}, "metadata": {}, "sourceType": "module", "externalDependencies": []}